import { defineStore } from "pinia";
import type { User, AuthState, AuthResponse } from "./types/auth.types";

export const useAuthStore = defineStore("auth", {
  state: (): AuthState => ({
    user: null as User | null,
    loggedIn: false,
  }),

  actions: {
    setAuthData(data: AuthResponse["data"]) {
      this.loggedIn = true;
      this.user = data;
    },

    clearAuth() {
      this.loggedIn = false;
    },
  },
  getters: { 
    getUser: (state) => state.user !== null,
  },

  persist: true,
});
