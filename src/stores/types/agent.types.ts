export interface Agent {
  snowID: string;
  name: string;
  status: "ACTIVE" | "INACTIVE" | string; // Adjust with actual enum if known
  widgetSettings: WidgetSettings;
  createdAt: string; // ISO date
  updatedAt: string; // ISO date
  tenant: Tenant;
  agentType: AgentType;
  agentPlatforms: AgentPlatformEntry[];
  workflow: Workflow | null;
}

export interface WidgetSettings {
  agent_name: string;
  agent_logo_url: string;
  welcome_message: string;
  agent_description: string;
  input_placeholder_text: string;
}

export interface Tenant {
  snowID: string;
  name: string;
}

export interface AgentType {
  snowID: string;
  name: string;
  tools: string[];
  prompts: string[];
}

export interface AgentPlatformEntry {
  config: Record<string, unknown>; // assuming it's a freeform config
  agentPlatform: AgentPlatform;
}

export interface AgentPlatform {
  snowID: string;
  name: string;
  configuration: {
    domain: string | null;
  };
  development_status: "LIVE" | "DEV" | string; // Update based on actual values
}

export interface ICreateAgent {
  name: string;
  agentTypeId: string;
  agentPlatforms: Array<{
    type: string;
    config?: Record<string, any>; // Made optional and flexible
  }>;
}

export interface Workflow {
  agentID: string;
  name: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}

export interface WorkflowNode {
  nodeId: string;
  type: string;
  config: Record<string, any>; // You can adjust this type if `config` has a specific structure
  settings: NodeSettings;
  positionX: number;
  positionY: number;
}

export interface NodeSettings {
  model?: Record<string, any>; // Define the type based on actual chatModel structure
  memory?: Record<string, any>; // Define the type if memory has a specific structure
  tool?: Record<string, any>; // Same as above for tool
  [key: string]: any; // Additional properties if needed
}

export interface WorkflowNodeModel {
  model?: string;
  temperature?: string;
  api_key?: string;
}
export interface WorkflowEdge {
  sourceNodeId: string;
  targetNodeId: string;
  sourceHandle: string;
  targetHandle: string;
  label: string;
}

export interface WorkflowUpdateI {
  workflowId: string;
  nodeId: string;
  type: string;
  integration_config: Record<string, any>; // Made more flexible to accept any config
  integration_settings: Record<string, any>;
  positionX: number;
  positionY: number;
}

export interface AgentType {
  id: string;
  name: string;
  description: string;
  status: string;
}

export interface AgentWidget {
  agent_name: string;
  agent_description: string;
  welcome_message: string;
  agent_logo_url: string;
  input_placeholder_text: string;
}

export interface AgentTypeDefinition {
  id: string;
  name: AgentTypeName;
  description: string;
  tools: string[];
  prompts: string[];
}

export interface ChatbotAnalytics {
  id: string | null;
  createdAt: Date;
  updatedAt: Date;
  chatbotId: number;
  inputTokens: bigint;
  outputTokens: bigint;
  totalTokens: bigint;
  inputCost: Number;
  outputCost: Number;
  totalCost: Number;
}

export interface ConversationMessage {
  id: string;
  content: string;
  sender: "USER" | "BOT" | "SYSTEM";
  createdAt: string;
}

export interface Conversation {
  id: string;
  userId: string;
  startedAt: string;
  endedAt: string | null;
  totalMessagePages: number;
  message: ConversationMessage;
}

export interface ConversationPagination {
  totalItems: number;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface ChatbotConversations {
  conversations: Conversation[];
  pagination: ConversationPagination;
}

export interface ConversationMessagesResponse {
  data: ConversationMessage[];
  pagination: {
    totalItems: number;
    currentPage: number;
    totalPages: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export type AgentTypeName = "conversation_agent" | "store_agent"; // Add more as needed
