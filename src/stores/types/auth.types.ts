import type { Country } from "./country.types";

export type UserRole = 'JOBSEEKER' | 'RECRUITER' | 'ADMIN' | 'HIRING_MANAGER';
export type WorkSetup = 'REMOTE' | 'HYBRID' | 'ON_SITE'
export type SalaryPerioud = "MONTHLY" | "YEARLY" | "HOURLY";

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  createdAt: string;
  role: UserRole;
  twoFactorEnabled: boolean;
  onboardingCompleted: boolean;
  phoneNumber: string | null;
  originLocation: string | null;
  preferredLocation: string | null;
  onboardingStepsCompleted: number;
  permissions: string[];
  accessToken?: string;
  refreshToken?: string;
  profile?: UserProfile;
}


export interface UserWithRelations extends User {
  profile: UserProfile;
}

export interface salaryRange {
  min: number;
  max: number;
}

export interface UserProfile {
  id: string;
  fromRangeSalary: string;
  toRangeSalary: string;
  currency: string;
  desiredSalaryFrequency: string;
  yearsOfExperience: string;
  workSetup: WorkSetup;
  dreamRole: string | null;
  profileCreatedAt: string;
}


export interface AuthState {
  user: User | null;
  loggedIn: boolean;
}


export type OAuthStatus = "success" | "false";

export type OAuthError =
  | "oauth_processing_failed"
  | "no_token"
  | "oauth_failed"
  | "invalid_provider";

export interface OAuthCallbackParams {
  status: OAuthStatus;
  error?: OAuthError;
  provider?: string;
}
