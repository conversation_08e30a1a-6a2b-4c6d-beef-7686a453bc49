export interface Country {
  id: string;
  name: string;
  code?: string;
  flag?: string;
  dialCode?: string;
}

// New type for the actual API response
export interface LocationSearchResult {
  id: string;
  name: string;
  city: string;
  district: string;
  zipCode: string;
  placeId: string;
  formattedAddress: string;
  country: {
    code: string;
    name: string;
  };
  state?: {
    code: string;
    name: string;
  };
  location: {
    lat: number;
    lng: number;
  };
}

export interface LocationSearchResponse {
  success: boolean;
  message: string;
  data: LocationSearchResult[];
}
