export interface Tool {
  snowID: string; // Tool ID
  name: string; // Tool name
  name_view: string; // Tool name for display
  category: string; // Tool category (e.g., "api", "plugin")
  description: string; // Tool description
  createdAt: string; // Creation date in ISO format
  updatedAt: string; // Last update date in ISO format
}

export interface ToolApiConfig {
  token: string;
  method: string;
  authType: string;
  endpoint: string;
  tokenType: string;
  authHeader: string;
  dynamicArrayFields: {};
}
export interface ToolStoreAddress {
  address: string;
  lat: string;
  long: string;
}
export interface ToolWithConfig<T = unknown> {
  tool: Tool;
  config: T;
}

export interface ToolResponse<T = unknown> {
  success: boolean;
  status: number;
  message: string;
  data: {
    tool: Tool;
    config: T;
  };
}
