export type WebSocketChannel =
  | "store"
  | "notifications"
  | "user"
  | "document"
  | "chat";

export type WebSocketMessageType =
  | "sync_status"
  | "sync_progress"
  | "sync_error"
  | "notification"
  | "chat_message"
  | "document-status-change";

export interface WebSocketMessage {
  type: WebSocketMessageType;
  data: unknown;
  timestamp: number;
  channel: WebSocketChannel;
  storeId?: string;
  documentId?: string; 
}

// Document status message structure
export interface DocumentStatusData {
  status: string;
  message: string;
}

export interface DocumentStatusMessage extends WebSocketMessage {
  type: "document-status-change";
  documentId: string;
  data: {
    type: string;
    documentId: string;
    timestamp: string;
    data: DocumentStatusData;
  };
}

export interface WebSocketConfig {
  reconnectAttempts: number;
  reconnectInterval: number;
  heartbeatInterval: number;
  baseUrl: string;
}

export interface ConnectionStatus {
  isConnected: boolean;
  lastConnected?: Date;
  reconnectAttempt: number;
  error?: string;
}

export interface StoreChannelParams {
  storeId: string;
  [key: string]: string; 
}

export type ConnectionStatusMap = {
  store: Record<string, ConnectionStatus>;
  user: ConnectionStatus;
  notifications: ConnectionStatus;
  document: ConnectionStatus;
  chat: ConnectionStatus;
};

export type MessageMap = Record<WebSocketChannel, WebSocketMessage[]>;
