import { defineStore } from 'pinia';
import type { WebSocketChannel, WebSocketMessage, ConnectionStatusMap, MessageMap, ConnectionStatus } from './types/websocket.types';

interface WebSocketState {
  connectionStatus: ConnectionStatusMap;
  messages: MessageMap;
  lastMessage: Record<WebSocketChannel, WebSocketMessage | null>;
}

export const useWebSocketStore = defineStore('websocket', {
  state: (): WebSocketState => ({
    connectionStatus: {
      'user': {
        isConnected: false,
        reconnectAttempt: 0
      },
      'store': {} as Record<string, ConnectionStatus>,
      'notifications': {
        isConnected: false,
        reconnectAttempt: 0
      },
      'document': {
        isConnected: false,
        reconnectAttempt: 0
      },
      'chat': {
        isConnected: false,
        reconnectAttempt: 0
      }
    },
    messages: {
      'user': [],
      'store': [],
      'notifications': [],
      'document': [],
      'chat': []
    },
    lastMessage: {
      'user': null,
      'store': null,
      'notifications': null,
      'document': null,
      'chat': null
    }
  }),

  actions: {
    setConnectionStatus(channel: WebSocketChannel, status: Partial<ConnectionStatus>, storeId?: string) {
      if (channel === 'store' && storeId) {
        this.connectionStatus[channel][storeId] = {
          ...this.connectionStatus[channel][storeId],
          ...status,
          lastConnected: status.isConnected ? new Date() : this.connectionStatus[channel][storeId]?.lastConnected
        };
      } else if (channel !== 'store') {
        this.connectionStatus[channel] = {
          ...this.connectionStatus[channel],
          ...status,
          lastConnected: status.isConnected ? new Date() : this.connectionStatus[channel].lastConnected
        };
      }
    },

    addMessage(channel: WebSocketChannel, message: WebSocketMessage) {
      this.messages[channel].push(message);
      this.lastMessage[channel] = message;

      // Keep only the last 100 messages per channel
      if (this.messages[channel].length > 100) {
        this.messages[channel] = this.messages[channel].slice(-100);
      }
    },

    clearMessages(channel: WebSocketChannel) {
      this.messages[channel] = [];
      this.lastMessage[channel] = null;
    },

    clearStoreConnection(storeId: string) {
      if (this.connectionStatus['store'][storeId]) {
        delete this.connectionStatus['store'][storeId];
      }
    }
  },

  getters: {
    getChannelMessages: (state) => {
      return (channel: WebSocketChannel) => state.messages[channel];
    },

    getStoreConnectionStatus: (state) => {
      return (storeId: string) => state.connectionStatus['store'][storeId] || {
        isConnected: false,
        reconnectAttempt: 0
      };
    },

    getChannelConnectionStatus: (state) => {
      return (channel: WebSocketChannel, storeId?: string) => {
        if (channel === 'store' && storeId) {
          return state.connectionStatus[channel][storeId];
        }
        return channel !== 'store' ? state.connectionStatus[channel] : null;
      };
    },

    getLastMessage: (state) => {
      return (channel: WebSocketChannel) => state.lastMessage[channel];
    }
  }
});
