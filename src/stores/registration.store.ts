import { defineStore } from "pinia";
import type { Country, LocationSearchResult } from "./types/country.types";
import type { WorkSetup } from "./types/auth.types";

// Minimal interfaces for registration-specific data
export interface PersonalInfo {
  name: string | null;
  country: Country | null;
  phoneNumber: string | null;
}

export interface JobPreferences {
  dreamJob: string | null;
  preferedLocation: LocationSearchResult | null;
  fromRangeSalary: string | null;
  toRangeSalary: string | null;
  experience: number;
  currency: string | null,
  workSetup: WorkSetup;
}

export interface Step {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

export interface RegistrationState {
  // Form data
  personalInfo: PersonalInfo;
  jobPreferences: JobPreferences;

  // Step management
  steps: Step[];
  currentStep: string;
}

export const useRegistrationStore = defineStore("registration", {
  state: (): RegistrationState => ({
    // Form data
    personalInfo: {
      name: null,
      country: null,
      phoneNumber: null,
    },
    jobPreferences: {
      dreamJob: null,
      preferedLocation: null,
      fromRangeSalary: null,
      toRangeSalary: null,
      experience: 0,
      currency: null,
      workSetup: "ON_SITE",
    },


    // Steps
    steps: [
      {
        id: "personal-info",
        title: "Let's get to know you",
        description:
          "Let's start with the basics — your name, location, and how to stay in touch.",
        completed: false,
      },
      {
        id: "job-preferences",
        title: "Your Dream Job, Your Rules",
        description:
          "Tell us what you're aiming for title, location, remote vibes — we've got your back.",
        completed: false,
      },
    ],
    currentStep: "personal-info",
  }),

  getters: {
    currentStepIndex(): number {
      return this.steps.findIndex((step) => step.id === this.currentStep);
    },

    isLastStep(): boolean {
      return this.currentStepIndex === this.steps.length - 1;
    },
  },

  actions: {
    // Personal info actions
    updatePersonalInfo(field: keyof PersonalInfo, value: any) {
      this.personalInfo[field] = value;
    },

    // Job preferences actions
    updateJobPreferences(field: keyof JobPreferences, value: any) {
      (this.jobPreferences as any)[field] = value;
    },

   

  

    // Step management actions
    goToNextStep() {
      const currentIndex = this.currentStepIndex;
      if (currentIndex < this.steps.length - 1) {
        // Mark current step as completed
        this.steps[currentIndex].completed = true;
        // Move to next step
        this.currentStep = this.steps[currentIndex + 1].id;
      }
    },

    goToPreviousStep() {
      const currentIndex = this.currentStepIndex;
      if (currentIndex > 0) {
        this.currentStep = this.steps[currentIndex - 1].id;
      }
    },

    // Activate step by index number (0-based)
    activateStep(stepIndex: number) {
      if (stepIndex >= 0 && stepIndex < this.steps.length) {
        // Mark all previous steps as completed
        for (let i = 0; i < stepIndex; i++) {
          this.steps[i].completed = true;
        }
        this.currentStep = this.steps[stepIndex].id;
      }
    },

   

    // Reset store
    resetRegistration() {
      this.personalInfo = {
        name: null,
        country: null,
        phoneNumber: null,
      };
      this.jobPreferences = {
        dreamJob: null,
        preferedLocation: null,
        fromRangeSalary: null,
        toRangeSalary: null,
        experience: 0,
        currency: null,
        workSetup: "ON_SITE",
      };
    
      this.currentStep = "personal-info";
      this.steps.forEach((step) => (step.completed = false));
    },
  },

  persist: true,
});
