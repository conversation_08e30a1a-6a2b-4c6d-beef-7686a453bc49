import { defineStore } from "pinia";
import { AgentService } from "@/services/agent.service";

import type {
  WidgetUpdateData,
  Agent,
  AgentType,
} from "@/stores/types/agent.types";


interface AgentState {
  currentAgent: any | null;
  agents: Agent[];
  agentTypes: any[];
  loading: boolean;
  error: string | null;
  widgetConfig: WidgetUpdateData | null;
  sdkInstance: any | null;
}

export const useAgentStore = defineStore("agent", {
  state: (): AgentState => ({
    currentAgent: null,
    agentTypes: [],
    agents: [],
    loading: false,
    error: null,
    widgetConfig: null,
    sdkInstance: null,
  }),

  actions: {
    setAgents(agent: Agent[]) {
      this.agents = agent;
    },
    setAgent(agent: Agent) {
      this.currentAgent = agent;
    },
    setAgentTypes(agentTypes: AgentType[]) {
      this.agentTypes = agentTypes;
    },

    clearCurrentAgent() {
      this.currentAgent = null;
      this.widgetConfig = null;
      this.unmountSDK();
    },

    async fetchWidgetConfig(chatbotId: string) {
      this.loading = true;
      this.error = null;
    },

    async updateWidgetConfig(config: WidgetUpdateData) {
      this.widgetConfig = config;
    },

    setSDKInstance(instance: any) {
      this.sdkInstance = instance;
    },

    unmountSDK() {
      if (!this.sdkInstance) return;
      this.sdkInstance.unmount();
      this.sdkInstance = null;
    },
  },

  getters: {
    hasAgent: (state) => state.currentAgent !== null,
    getAgentName: (state) => state.currentAgent?.name || "Unknown Agent",
    getAgentCreationDate: (state) => {
      if (!state.currentAgent?.createdAt) return "";
      return new Date(state.currentAgent.createdAt).toLocaleDateString();
    },
    getCurrentAgent: (state) => state.currentAgent,
    getAgentTypes: (state) => state.agentTypes || [],
    getAgentId: (state) => state.currentAgent?.snowID || null,
    getWidgetConfig: (state) => state.widgetConfig,
    getSDKInstance: (state) => state.sdkInstance,
    hasSDKInstance: (state) => state.sdkInstance !== null,
  },
  persist: true,
});
