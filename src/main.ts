import { createApp } from "vue";
import { createPinia } from "pinia";
import { createI18n } from "vue-i18n";
import en from "./i18n/en";

const i18n = createI18n({
  legacy: false,
  locale: "en",
  messages: {
    en,
  },
});
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import App from "./App.vue";
import router from "./router";
import "./assets/scss/main.scss";

// Stagewise toolbar for development - controlled by environment variable boni is using this it wont effect anything in production
if (import.meta.env.VITE_ENABLE_STAGEWISE_TOOLBAR === "true") {
  // @ts-ignore - Dynamic import for development toolbar
  import("@stagewise/toolbar-vue")
    .then((module: any) => {
      const { StagewiseToolbar } = module;
      const stagewiseConfig = {
        plugins: [],
      };

      // Create a separate container for the stagewise toolbar
      const toolbarContainer = document.createElement("div");
      toolbarContainer.id = "stagewise-toolbar";
      document.body.appendChild(toolbarContainer);

      // Create a separate Vue app instance for the toolbar
      const toolbarApp = createApp(StagewiseToolbar, {
        config: stagewiseConfig,
      });
      toolbarApp.mount("#stagewise-toolbar");
    })
    .catch((error) => {
      console.warn("Failed to load stagewise toolbar:", error);
    });
}

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

const app = createApp(App);
app.use(pinia);
app.use(i18n);
app.use(router);
app.mount("#app");
