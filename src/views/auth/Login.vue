<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import <PERSON><PERSON><PERSON><PERSON> from "@/components/talentflow/button.vue";
import B_Input from "@55/input.vue";
import Divider from "@55/divider.vue";
import { ref, onMounted } from "vue";
import { z } from "zod";
import { AuthService } from "@/services";

import { useForm, useField } from "vee-validate";
import { toTypedSchema } from "@vee-validate/zod";
import LogoImage from "@/components/ff/logo-image.vue";

const router = useRouter();
const route = useRoute();
const isLoading = ref(false);
const showResetSuccess = ref(false);

const isPasswordVisible = ref(false);
// B_Input refs
const emailInputRef = ref();
// Translation
import { useI18n } from "vue-i18n";
const { t } = useI18n();

import type { ProviderLoginTypes } from "@/types/general.types";

// Check for reset success parameter
onMounted(() => {
  showResetSuccess.value = route.query.reset === "success";
  emailInputRef.value.focus();
});

const loginSchema = toTypedSchema(
  z.object({
    email: z.string().email(t("validations.email")),
    password: z.string().min(3, t("validations.min", { min: 5 })),
  })
);

const { errors, meta, handleSubmit } = useForm({
  validationSchema: loginSchema,
  initialValues: {
    email: "<EMAIL>",
    password: "password",
  },
});

const { value: email, errorMessage: emailError } = useField<string>("email");
const { value: password, errorMessage: passwordError } =
  useField<string>("password");

const clearEmailField = () => {
  email.value = "";
};

const onSubmit = handleSubmit(async (values) => {
  isLoading.value = true;
  try {
    const response = await AuthService.login({
      email: values.email,
      password: values.password,
    });
    console.log(response);
    if (response.success && response.data.accessToken) {
      const role = response.data.user.profile?.role;
      if (role == "TENANT") {
        router.push({ name: "dashboard" });
      } else if (role == "ADMIN") {
        router.push({ name: "cron_jobs" });
      }
    }
  } catch (error) {
    // Handle login error
  } finally {
    isLoading.value = false;
  }
});

const togglePasswordVisibility = () => {
  isPasswordVisible.value = !isPasswordVisible.value;
};
</script>

<template>
  <!-- Centered Layout Container -->
  <div class="auth-container">
    <div class="auth-wrapper">
      <!-- Login Card -->
      
        <!-- Header -->
        <div class="text-center">
          <LogoImage />
          <h4 class="mb-1 mt-3">Welcome back</h4>
          <p class="mb-2">Login with your Google account</p>
        </div>

        <!-- Form -->
        <form @submit="onSubmit" class="auth-form">
          <!-- Google Login Button -->

          <B_Button
            :is-submit="true"
            type="secondary"
            size="lg"
            :is-box="false"
            class="mb-1"
            :fullwidth="true"
            :isLoading="isLoading"
            left-icon="google"
          >
            <template v-if="isLoading">
              <ff-loader size="sm" />
            </template>
            Continue with Google 
          </B_Button>
          <B_Button
            type="secondary"
            size="lg"
            :is-box="false"
            class="mb-2"
            :fullwidth="true"
            :isLoading="isLoading"
            leftIcon="linkedin"
            @click="AuthService.loginWithProvider('linkedin', 'js')"
          >
            <template v-if="isLoading">
              <ff-loader size="sm" />
            </template>
            Continue with Linkedin
          </B_Button>

          <!-- Divider -->
          <Divider text="or" />

          <!-- Email B_Input -->
          <div class="form-inputs mt-4">
            <B_Input
              :label="t('inputs.email')"
              :autocomplete="'email'"
              v-model="email"
              type="text"
              
              ref="emailInputRef"
              id="email"
              :placeholder="t('inputs.email_placeholder')"
              :disabled="isLoading"
              :extra-class="'mb-2'"
              :error-message="emailError"
              @iconClick="clearEmailField"
              :right-icon="email ? 'close' : ''"
            />

            <!-- Password B_Input -->
            <div class="">
              <div
                class="password-header ff-flex ff-flex-align-center ff-space-between mb-1"
              >
                <label for="password" class="password-label">Password</label>

                <B_Button
                  to="/auth/reset-password"
                  type="flat"
                  size="md"
                  :is-box="false"
                >
                  <template v-if="isLoading">
                    <ff-loader size="sm" />
                  </template>
                  <template v-else> Forgot your password? </template>
                </B_Button>
              </div>
              <B_Input
                id="password"
                v-model="password"
                placeholder="Enter your password"
                :type="isPasswordVisible ? 'text' : 'password'"
                auto-capitalize="none"
                auto-complete="current-password"
                :disabled="isLoading"
                :right-icon="isPasswordVisible ? 'eyeClosed' : 'eyeOpen'"
                @iconClick="togglePasswordVisibility"
                :error-message="passwordError"
                left-icon="lock"
              />
            </div>

            <!-- Submit Button -->
            <B_Button
              :is-submit="true"
              type="primary"
              size="lg"
              :is-box="false"
              class="mt-2"
              :fullwidth="true"
              leftIcon="login"
              :isLoading="isLoading"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              
                {{ t("general.auth.login") }}
            </B_Button>
          </div>

          <!-- Sign up link -->
          <div class="text-center">
            <p class="mt-2">
              <B_Button
                to="/auth/signup"
                type="flat"
                size="lg"
                :is-box="false"
                class="mb-2"
                :fullwidth="true"
              >
                I don't have an account.
              </B_Button>
            </p>
          </div>
        </form>

      
    </div>
  </div>
</template>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;
</style>
