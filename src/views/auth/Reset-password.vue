<script setup lang="ts">
import { useRouter } from "vue-router";
import <PERSON>_<PERSON><PERSON> from "@/components/talentflow/button.vue";
import B_Input from "@55/input.vue";
import { ref, onMounted } from "vue";
import { z } from "zod";
import { AuthService } from "@/services";
import { Vue3Lottie } from "vue3-lottie";
import emailAnimation from "../../../public/animations/email.json";
import { useForm, useField } from "vee-validate";
import { toTypedSchema } from "@vee-validate/zod";
import LogoImage from "@/components/ff/logo-image.vue";

// Translation
import { useI18n } from "vue-i18n";
const { t } = useI18n();

interface TokenVerificationResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    success: boolean;
    email: string;
    token: string;
  };
}

const router = useRouter();
const isLoading = ref(false);
const isEmailSent = ref(false);
const isVerifying = ref(false);
const isTokenValid = ref(false);
const resetToken = ref("");

// Forgot Password Schema
const forgotPasswordSchema = toTypedSchema(
  z.object({
    email: z.string().email(t("validations.email")),
  })
);

// Reset Password Schema
const resetPasswordSchema = toTypedSchema(
  z
    .object({
      password: z.string().min(8, "Password must be at least 8 characters"),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    })
);

// Forgot Password Form
const { handleSubmit: handleForgotSubmit } = useForm({
  validationSchema: forgotPasswordSchema,
  initialValues: {
    email: "",
  },
});

const { value: email, errorMessage: emailError } = useField<string>("email");

// Reset Password Form
const { handleSubmit: handleResetSubmit } = useForm({
  validationSchema: resetPasswordSchema,
  initialValues: {
    password: "",
    confirmPassword: "",
  },
});

const { value: password, errorMessage: passwordError } =
  useField<string>("password");
const { value: confirmPassword, errorMessage: confirmPasswordError } =
  useField<string>("confirmPassword");

const onSubmit = handleForgotSubmit(async (values) => {
  isLoading.value = true;
  try {
    // Add artificial delay of 1 second
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const response = await AuthService.initForgetPassword({
      email: values.email,
      captchaToken: "1231231231231",
    });
    console.log(response);
    isEmailSent.value = true;
  } catch (error) {
    // Handle error
  } finally {
    isLoading.value = false;
  }
});

const onResetSubmit = handleResetSubmit(async (values) => {
  isLoading.value = true;
  try {
    const response = await AuthService.finishResetPassword({
      token: resetToken.value,
      newPassword: values.password,
      email: email.value,
    });
    console.log(response);
    if (response.success) {
      router.push("/auth/login?reset=success");
    }
  } catch (error) {
    // Handle error
    console.error("Failed to reset password:", error);
  } finally {
    isLoading.value = false;
  }
});

const verifyToken = async () => {
  const token = new URLSearchParams(window.location.search).get("t");
  if (token) {
    isVerifying.value = true;
    try {
      const response = (await AuthService.confirmResetToken({
        token: token,
      })) as unknown as TokenVerificationResponse;

      if (response.success && response.data) {
        isTokenValid.value = true;
        resetToken.value = response.data.token; // New token after confirming other token
        email.value = response.data.email;
      }
    } catch (error) {
      console.log(error);
    } finally {
      isVerifying.value = false;
    }
  }
};

onMounted(() => {
  verifyToken();
});
</script>

<template>
  <!-- Centered Layout Container -->
  <div class="auth-container">
    <div class="auth-wrapper">
      <!-- Reset Password Card -->
      <div class="">
        <!-- Header -->
        <div class="text-center">
          <LogoImage />
          <h5 class="mb-1 mt-5">
            {{
              isVerifying
                ? "Verifying..."
                : isTokenValid
                ? "Reset Password"
                : "Let's Get You a New Password"
            }}
          </h5>
          <p class="mb-2" v-if="!isVerifying && !isTokenValid">
            No worries — it happens! Just enter your email and we'll take care
            of the rest.
          </p>
          <p class="mb-2" v-if="!isVerifying && isTokenValid">
            Please enter your new password
          </p>
        </div>

        <!-- Verification Loading -->
        <div v-if="isVerifying" class="text-center py-4">
          <div class="mb-4">
            Please wait while we verify your reset password link...
          </div>
        </div>

        <!-- Forgot Password Form -->
        <form
          @submit="onSubmit"
          v-else-if="!isEmailSent && !isTokenValid"
          class="auth-form"
        >
          <div class="form-inputs">
            <B_Input
              :label="t('inputs.email')"
              :autocomplete="'email'"
              v-model="email"
              type="email"
              id="email"
              placeholder="Enter your email address"
              :disabled="isLoading"
              :extra-class="'mb-2'"
              :error-message="emailError"
            />

            <!-- Submit Button -->
            <B_Button
              :is-submit="true"
              type="primary"
              size="lg"
              :is-box="false"
              class="mt-2"
              :fullwidth="true"
              leftIcon="login"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Send Reset Link </template>
            </B_Button>
          </div>

          <!-- Back to Login link -->
          <div class="text-center">
            <p class="mt-2">
              <B_Button
                to="/auth/login"
                :is-route="true"
                type="flat"
                size="lg"
                :is-box="false"
                class="mb-2"
                :fullwidth="true"
              >
                <template v-if="isLoading">
                  <ff-loader size="sm" />
                </template>
                <template v-else> Remember your password? Log in </template>
              </B_Button>
            </p>
          </div>
        </form>

        <!-- Reset Password Form -->
        <form
          @submit="onResetSubmit"
          v-else-if="isTokenValid"
          class="auth-form"
        >
          <!-- Header for Set New Password -->
          <div class="text-center mb-6">
            <h3 class="text-2xl font-semibold mb-2">One Last Step</h3>
            <p class="text-gray-600 mb-1">
              This will replace your old password.
            </p>
            <p class="text-gray-600">Just confirm it and you're good to go.</p>
          </div>

          <div class="form-inputs">
            <!-- New Password Field -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-900 mb-2">
                New password
              </label>
              <B_Input
                v-model="password"
                type="password"
                id="password"
                placeholder="Enter your new password"
                :disabled="isLoading"
                :extra-class="'mb-1'"
                :error-message="passwordError"
                left-icon="lock"
                right-icon="eye"
              />
              <p class="text-xs text-gray-500 mt-1">
                Password must be at least 8 characters, and include a number or
                symbol.
              </p>
            </div>

            <!-- Confirm Password Field -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-900 mb-2">
                Confirm new password
              </label>
              <B_Input
                v-model="confirmPassword"
                type="password"
                id="confirmPassword"
                placeholder="Enter your new password"
                :disabled="isLoading"
                :error-message="confirmPasswordError"
                left-icon="lock"
                right-icon="eye"
              />
            </div>

            <!-- Submit Button -->
            <B_Button
              :is-submit="true"
              type="primary"
              size="lg"
              :is-box="false"
              class="mb-6"
              :fullwidth="true"
              :disabled="isLoading"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Set a New Password </template>
            </B_Button>

            <!-- Back to Login Link -->
            <div class="text-center">
              <router-link
                to="/auth/login"
                class="text-sm text-gray-600 hover:text-gray-900 underline underline-offset-4"
              >
                Remembered it? Back to login
              </router-link>
            </div>
          </div>
        </form>

        <!-- Email Sent Success -->
        <div v-else-if="!isTokenValid" class="text-center auth-form">
          <div class="w-full mx-auto mb-6 relative">
            <Vue3Lottie
              :animationData="emailAnimation"
              height="100%"
              width="100%"
              :loop="true"
              :autoplay="true"
            />
          </div>
          <div class="mb-4 text-sm">
            <h4>Check Your Email</h4>
            <p>
              You'll find a reset link in your inbox. Click it to choose a new
              password — we'll wait right here
            </p>
          </div>
          <B_Button
            to="/auth/login"
            :is-route="true"
            type="secondary"
            size="lg"
            :is-box="false"
            class="mt-2"
            :fullwidth="true"
          >
            Remembered it? Back to login
          </B_Button>
        </div>

        <!-- Terms -->
        <!-- <div class="text-center">
          <p class="">
            By clicking continue, you agree to our
            <B_Button
              to="/auth/signup"
              :is-route="true"
              type="flat"
              size="lg"
              :is-box="false"
              class="mb-2"
              :fullwidth="true"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Terms & Services </template>
            </B_Button>
            &
            <B_Button
              to="/auth/signup"
              :is-route="true"
              type="flat"
              size="lg"
              :is-box="false"
              class="mb-2"
              :fullwidth="true"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Privacy Policy </template>
            </B_Button>
            .
          </p>
        </div> -->
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;
</style>
