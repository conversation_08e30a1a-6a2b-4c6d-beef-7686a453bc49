<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import <PERSON><PERSON><PERSON><PERSON> from "@/components/talentflow/button.vue";
import B_Input from "@55/input.vue";
import { ref, onMounted } from "vue";
import { z } from "zod";
import { AuthService } from "@/services";
import { useForm, useField } from "vee-validate";
import { toTypedSchema } from "@vee-validate/zod";
import LogoImage from "@/components/ff/logo-image.vue";

// Translation
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const router = useRouter();
const route = useRoute();
const isLoading = ref(false);
const token = ref("");
const userEmail = ref("");
const isActivation = ref(false); // true for signup activation, false for password reset
const isPasswordVisible = ref(false);

// Set Password Schema
const setPasswordSchema = toTypedSchema(
  z
    .object({
      password: z.string().min(8, "Password must be at least 8 characters"),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    })
);

// Form Setup
const { handleSubmit, meta } = useForm({
  validationSchema: setPasswordSchema,
  initialValues: {
    password: "",
    confirmPassword: "",
  },
});

const { value: password, errorMessage: passwordError } =
  useField<string>("password");
const { value: confirmPassword, errorMessage: confirmPasswordError } =
  useField<string>("confirmPassword");

const onSubmit = handleSubmit(async (values) => {
  isLoading.value = true;
  try {
    if (isActivation.value) {
      // Handle signup activation - using finishResetPassword for now
      const response = await AuthService.finishResetPassword({
        token: token.value,
        newPassword: values.password,
        email: userEmail.value,
      });
      if (response.success) {
        router.push("/auth/login?activation=success");
      }
    } else {
      // Handle password reset
      const response = await AuthService.finishResetPassword({
        token: token.value,
        newPassword: values.password,
        email: userEmail.value,
      });
      if (response.success) {
        router.push("/auth/login?reset=success");
      }
    }
  } catch (error) {
    console.error("Failed to set password:", error);
  } finally {
    isLoading.value = false;
  }
});

onMounted(() => {
  // Get token and determine if this is activation or reset
  const urlToken = route.query.t as string;
  const activationToken = route.query.activation as string;

  if (activationToken) {
    token.value = activationToken;
    isActivation.value = true;
  } else if (urlToken) {
    token.value = urlToken;
    isActivation.value = false;
  }

  // You might want to verify the token here and get user email
});

const togglePasswordVisibility = () => {
  isPasswordVisible.value = !isPasswordVisible.value;
};
</script>

<template>
  <!-- Centered Layout Container -->
  <div class="auth-container">
    <div class="auth-wrapper">
      <!-- Set Password Card -->
      <div class="">
        <!-- Header -->
        <div class="text-center">
          <LogoImage />
          <h3 class="mb-1">One Last Step</h3>
          <p class="mb-2">This will replace your old password.</p>
          <p class="mb-2">Just confirm it and you're good to go.</p>
        </div>

        <!-- Form -->
        <form @submit="onSubmit" class="auth-form">
          <!-- Form Inputs -->
          <div class="form-inputs">
            <!-- New Password Input -->
            <div class="mb-2">
              <B_Input
                id="password"
                :label="t('inputs.new_password')"
                v-model="password"
                placeholder="Enter your new password"
                :type="isPasswordVisible ? 'text' : 'password'"
                auto-capitalize="none"
                auto-complete="new-password"
                :disabled="isLoading"
                :extra-class="'mb-1'"
                :error-message="passwordError"
                :right-icon="isPasswordVisible ? 'eyeClosed' : 'eyeOpen'"
                @iconClick="togglePasswordVisibility"
              />
              <p class="text-xs text-gray-500">
                Password must be at least 8 characters, and include a number or
                symbol.
              </p>
            </div>

            <!-- Confirm Password Input -->
            <div class="mb-2">
              <B_Input
                id="confirmPassword"
                :label="t('inputs.confirm_password')"
                v-model="confirmPassword"
                :placeholder="t('inputs.confirm_password_placeholder')"
                :type="isPasswordVisible ? 'text' : 'password'"
                auto-capitalize="none"
                auto-complete="new-password"
                :disabled="isLoading"
                :error-message="confirmPasswordError"
                :right-icon="isPasswordVisible ? 'eyeClosed' : 'eyeOpen'"
                @iconClick="togglePasswordVisibility"
              />
            </div>

            <!-- Submit Button -->
            <B_Button
              :is-submit="true"
              type="primary"
              size="lg"
              :is-box="false"
              class="mt-2"
              :fullwidth="true"
              leftIcon="login"
              :disabled="!meta.valid || isLoading"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Set a New Password </template>
            </B_Button>
          </div>

          <!-- Back to Login link -->
          <div class="text-center">
            <p class="mt-2">
              <B_Button
                to="/auth/login"
                :is-route="true"
                type="flat"
                size="lg"
                :is-box="false"
                class="mb-2"
                :fullwidth="true"
              >
                <template v-if="isLoading">
                  <ff-loader size="sm" />
                </template>
                <template v-else> Remembered it? Back to login </template>
              </B_Button>
            </p>
          </div>
        </form>

        <!-- Terms -->
        <!-- <div class="text-center">
          <p class="">
            By clicking continue, you agree to our
            <B_Button
              to="/auth/signup"
              :is-route="true"
              type="flat"
              size="lg"
              :is-box="false"
              class="mb-2"
              :fullwidth="true"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Terms & Services </template>
            </B_Button>
            &
            <B_Button
              to="/auth/signup"
              :is-route="true"
              type="flat"
              size="lg"
              :is-box="false"
              class="mb-2"
              :fullwidth="true"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Privacy Policy </template>
            </B_Button>
            .
          </p>
        </div> -->
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;
</style>
