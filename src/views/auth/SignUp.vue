<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import <PERSON>_<PERSON>ton from "@/components/talentflow/button.vue";
import B_Input from "@55/input.vue";
import { ref, onMounted } from "vue";
import { z } from "zod";
import { AuthService } from "@/services";
import { useForm, useField } from "vee-validate";
import { toTypedSchema } from "@vee-validate/zod";
import { Vue3Lottie } from "vue3-lottie";
import emailAnimation from "../../../public/animations/email.json";
import Divider from "@55/divider.vue";
import LogoImage from "@/components/ff/logo-image.vue";
const router = useRouter();
const route = useRoute();
const isLoading = ref(false);
const showResetSuccess = ref(false);
const isEmailSent = ref(false);

// B_Input refs
const emailInputRef = ref();
// Translation
import { useI18n } from "vue-i18n";
const { t } = useI18n();

// Check for reset success parameter
onMounted(() => {
  showResetSuccess.value = route.query.reset === "success";
  emailInputRef.value.focus();
});

const signupSchema = toTypedSchema(
  z.object({
    email: z.string().email(t("validations.email")),
    password: z.string().min(3, t("validations.min", { min: 5 })),
  })
);

const { handleSubmit } = useForm({
  validationSchema: signupSchema,
  initialValues: {
    email: "",
    password: "",
  },
});

const { value: email, errorMessage: emailError } = useField<string>("email");
const { value: password } = useField<string>("password");

const clearEmailField = () => {
  email.value = "";
};

// const onSubmit = handleSubmit(async (values) => {
//   isLoading.value = true;
//   try {
//     const response = await AuthService.signup({
//       email: values.email,
//       password: values.password,
//     });
//     console.log(response);
//     if (response.success && response.data.accessToken) {
//       const { role } = response.data.user;
//       if (role == "TENANT") {
//         router.push({ name: "dashboard" });
//       } else if (role == "ADMIN") {
//         router.push({ name: "cron_jobs" });
//       }
//     } else {
//       // If signup requires email verification, show email confirmation
//       isEmailSent.value = true;
//     }
//   } catch (error) {
//     // Handle signup error - show email confirmation for now
//     isEmailSent.value = true;
//   } finally {
//     isLoading.value = false;
//   }
// });
</script>

<template>
  <!-- Centered Layout Container -->
  <div class="auth-container">
    <div class="auth-wrapper">
      <!-- Signup Card -->
      <div class="">
        <!-- Header -->
        <div class="text-center">
          <LogoImage />
          <h4 class="mb-1 mt-3" v-if="!isEmailSent">
            Land Your
            <br />
            Dream Job Smarter
          </h4>
          <h5 class="mb-1" v-else>Check Your Email</h5>
          <p class="mb-2" v-if="!isEmailSent">
            Let AI take the stress out of job hunting. Build smarter
            applications and ace interviews with ease.
          </p>
          <p class="mb-2" v-else>
            We've sent you a confirmation email. Please check your inbox to
            complete your signup.
          </p>
        </div>

        <!-- Email Sent Success -->
        <div v-if="isEmailSent" class="text-center auth-form">
          <div class="w-full mx-auto mb-6 relative">
            <Vue3Lottie
              :animationData="emailAnimation"
              height="100%"
              width="100%"
              :loop="true"
              :autoplay="true"
            />
          </div>
          <div class="mb-4 text-sm">
            <h4>Activate your account</h4>
            <p>
              You'll find an activation link in your inbox. Click it to activate
              your account and start your journey.
            </p>
          </div>
          <B_Button
            to="/auth/login"
            :is-route="true"
            type="secondary"
            size="lg"
            :is-box="false"
            class="mt-2"
            :fullwidth="true"
          >
            Back to Login
          </B_Button>
        </div>

        <!-- Signup Form -->
        <form @submit="onSubmit" class="auth-form" v-else>
          <!-- Google Login Button -->
          <B_Button
            :is-submit="true"
            type="secondary"
            size="lg"
            :is-box="false"
            class="mb-1"
            :fullwidth="true"
            leftIcon="google"
          >
            <template v-if="isLoading">
              <ff-loader size="sm" />
            </template>
            <template v-else> Continue with Google </template>
          </B_Button>
          <B_Button
            :is-submit="true"
            type="secondary"
            size="lg"
            :is-box="false"
            class="mb-2"
            :fullwidth="true"
            leftIcon="linkedin"
          >
            <template v-if="isLoading">
              <ff-loader size="sm" />
            </template>
            <template v-else> Continue with Linkedin </template>
          </B_Button>

          <!-- Divider -->
          <Divider text="or" />

          <!-- Email B_Input -->
          <div class="form-inputs mt-4">
            <B_Input
              :label="t('inputs.email')"
              :autocomplete="'email'"
              v-model="email"
              type="text"
              ref="emailInputRef"
              id="email"
              :placeholder="t('inputs.email_placeholder')"
              :disabled="isLoading"
              :extra-class="'mb-2'"
              :error-message="emailError"
              @iconClick="clearEmailField"
              :right-icon="email ? 'close' : ''"
            />

            <!-- Password B_Input -->

            <!-- Submit Button for Signup -->
            <B_Button
              :is-submit="true"
              type="primary"
              size="lg"
              :is-box="false"
              class="mt-2"
              :fullwidth="true"
              leftIcon="login"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else>
                {{ t("general.auth.signup") }}
              </template>
            </B_Button>

            <!-- Forgot Password Button - Outside the form -->
            <!-- <div class="text-center mt-4">
              <B_Button
                to="/auth/reset-password"
                :is-route="true"
                type="flat"
                size="lg"
                :is-box="false"
                class="mb-2"
                :fullwidth="true"
              >
                Forgot your password?
              </B_Button>
            </div> -->
          </div>

          <!-- Sign up link -->
          <div class="text-center">
            <p class="mt-2">
              <B_Button
                to="/auth/login"
                :is-route="true"
                type="flat"
                size="lg"
                :is-box="false"
                class="mb-2"
                :fullwidth="true"
              >
                <template v-if="isLoading">
                  <ff-loader size="sm" />
                </template>
                <template v-else> I already have an account. </template>
              </B_Button>
            </p>
          </div>
        </form>

        <!-- Terms -->
        <!-- <div class="text-center" v-if="!isEmailSent">
          <p class="">
            By clicking continue, you agree to our
            <B_Button
              to="/auth/signup"
              :is-route="true"
              type="flat"
              size="lg"
              :is-box="false"
              class="mb-2"
              :fullwidth="true"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Terms & Services </template>
            </B_Button>
            &
            <B_Button
              to="/auth/signup"
              :is-route="true"
              type="flat"
              size="lg"
              :is-box="false"
              class="mb-2"
              :fullwidth="true"
            >
              <template v-if="isLoading">
                <ff-loader size="sm" />
              </template>
              <template v-else> Privacy Policy </template>
            </B_Button>
            .
          </p>
        </div> -->
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;

.auth-container {
  min-height: 100%;
  width: 400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
