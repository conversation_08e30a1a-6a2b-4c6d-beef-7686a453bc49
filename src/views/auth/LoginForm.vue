<!-- <script setup lang="ts">
import Button from "../../components/ff/button.vue";
import Input from "../../components/ff/input.vue";
import { useRouter } from "vue-router";
import { ref, reactive } from "vue";
import { z } from "zod";
import { AuthService } from "@/services";

const router = useRouter();
const isLoading = ref(false);
const errors = reactive({
  email: "",
  password: "",
});
const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(3, "Password must be at least 6 characters"),
});

const formData = reactive({
  email: "tenant@.studio",
  password: "password",
});

const validateForm = () => {
  try {
    loginSchema.parse(formData);
    errors.email = "";
    errors.password = "";
    return true;
  } catch (error) {
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        if (err.path[0] === "email") errors.email = err.message;
        if (err.path[0] === "password") errors.password = err.message;
      });
    }
    return false;
  }
};

const onSubmit = async (event: Event) => {
  event.preventDefault();

  if (!validateForm()) return;

  isLoading.value = true;
  try {
    const response = await AuthService.login({
      email: formData.email,
      password: formData.password,
    });
    console.log(response);
    if (response.success && response.data.accessToken) {
      router.push({ name: "dashboard" });
    }
  } catch (error) {
    // Handle login error
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <div class="flex flex-col gap-6">
    <div class="card">
      <div class="card-header text-center">
        <h1 class="card-title text-xl">Welcome back</h1>
        <p class="card-description">Login with your Google account</p>
      </div>
      <div class="card-content">
        <form @submit="onSubmit">
          <div class="grid gap-6">
            <div class="flex flex-col gap-4">
              <Button type="white" class="w-full">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path
                    d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                    fill="currentColor"
                  />
                </svg>
                Login with Google
              </Button>
            </div>
            <div
              class="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border"
            >
              <span
                class="relative z-10 bg-background px-2 text-muted-foreground"
              >
                Or continue with
              </span>
            </div>
            <div class="grid gap-6">
              <div class="grid gap-2">
                <label for="email">Email</label>
                <Input
                  id="email"
                  v-model="formData.email"
                  placeholder="<EMAIL>"
                  type="email"
                  auto-capitalize="none"
                  auto-complete="email"
                  auto-correct="off"
                  :disabled="isLoading"
                  @blur="validateForm"
                />
              </div>
              <div class="grid gap-2">
                <div class="flex items-center">
                  <label for="password">Password</label>
                  <router-link
                    to="/auth/reset-password"
                    href="#"
                    class="ml-auto text-sm underline-offset-4 hover:underline"
                  >
                    Forgot your password?
                  </router-link>
                </div>
                <Input
                  id="password"
                  v-model="formData.password"
                  placeholder="Enter your password"
                  type="password"
                  auto-capitalize="none"
                  auto-complete="current-password"
                  :disabled="isLoading"
                  @blur="validateForm"
                />
              </div>
              <Button type="primary" :is-submit="true" class="w-full">
                Login
              </Button>
            </div>
            <div class="text-center text-sm">
              Don't have an account?
              <router-link
                to="/auth/signup"
                class="underline underline-offset-4"
                >Sign up</router-link
              >
            </div>
          </div>
        </form>
      </div>
    </div>
    <div
      class="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 [&_a]:hover:text-primary"
    >
      By clicking continue, you agree to our
      <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>.
    </div>
  </div>
</template> -->
