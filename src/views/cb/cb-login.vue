<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import { ref, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import <PERSON>_Button from "@/components/talentflow/button.vue";
import FfDots from "@55/dots.vue";
import LogoImage from "@/components/ff/logo-image.vue";
import { AuthService } from "@/services/auth.service";
import type { OAuthStatus, OAuthError } from "@/stores/types/auth.types";
import type { ProviderLoginTypes, RoleShortened } from "@/types/general.types";

const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const provider = ref<ProviderLoginTypes | null>(null);
const role = ref<RoleShortened | null>(null);
// Status tracking
const currentStatus = ref<string>("loading");
const errorType = ref<OAuthError | "">("");
const isLoading = ref(true);

// Parse query parameters and determine status
onMounted(() => {
  const status = route.query.status as string;
  provider.value = route.query.provider as ProviderLoginTypes;
  role.value = route.query.role as RoleShortened;
  if (role.value === "js") {
    role.value = "JOBSEEKER";
  } else if (role.value === "hr") {
    role.value = "RECRUITER";
  } else if (role.value === "admin") {
    role.value = "ADMIN";
  }
  const error = route.query.error as OAuthError;

  // Set initial loading state
  setTimeout(() => {
    isLoading.value = false;

    if (status === "success") {
      currentStatus.value = "loading"; // Stay in loading state until /me succeeds
      // Call me endpoint immediately, don't show success yet
      setTimeout(async () => {
        try {
          await AuthService.me();
          // Only now show success and redirect
          currentStatus.value = "success";
          isLoading.value = false;
        } catch (error) {
          console.error("Failed to fetch user data:", error);
          // If /me fails after "success" status, treat it as an OAuth processing error
          currentStatus.value = "error";
          errorType.value = "oauth_processing_failed";
          isLoading.value = false;
        }
      }, 2500); // Keep the same timing
    } else if (status === "false" && error) {
      currentStatus.value = "error";
      errorType.value = error;
    } else {
      currentStatus.value = "loading";
    }
  }, 1500);
});

// Computed properties for dynamic content
const statusContent = computed(() => {
  switch (currentStatus.value) {
    case "success":
      return {
        title: "Authentication Successful",
        message: "Welcome! Redirecting to your dashboard...",
        animation: "success",
      };
    case "error":
      // All OAuth errors show the same processing failed message
      return {
        title: "Processing Failed",
        message:
          "We encountered an issue while processing your authentication. Please try again.",
        animation: "error",
      };
    default:
      return {
        title: "Verifying",
        message: "Please wait while we verify your credentials",
        animation: "loading",
      };
  }
});
</script>

<template>
  <!-- Centered Layout Container -->
  <div class="auth-container">
    <div class="auth-wrapper">
      <!-- Login Card -->
      <div class="">
        <!-- Header -->
        <div class="text-center">
          <LogoImage />

          <!-- Dynamic Content -->
          <h4 class="mb-1 mt-3">{{ statusContent.title }}</h4>
          <p class="mb-2">{{ statusContent.message }}</p>

          <!-- Dynamic Animation -->
          <div class="animation-container">
            <!-- Loading Animation - 3 Pulsating Dots -->
            <div
              v-if="isLoading || currentStatus === 'loading'"
              class="loading-animation"
            >
              <FfDots size="md" />
            </div>

            <!-- Success Animation -->
            <div
              v-else-if="currentStatus === 'success'"
              class="success-animation"
            >
              <svg class="checkmark" width="60" height="60" viewBox="0 0 60 60">
                <circle
                  class="checkmark-circle"
                  cx="30"
                  cy="30"
                  r="25"
                  fill="none"
                  stroke="#10B981"
                  stroke-width="3"
                />
                <path
                  class="checkmark-check"
                  d="M18 30 L25 37 L42 20"
                  fill="none"
                  stroke="#10B981"
                  stroke-width="3"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>

            <!-- Processing Failed Animation - for all OAuth errors -->
            <div v-else-if="currentStatus === 'error'" class="error-animation">
              <svg
                class="error-cross"
                width="60"
                height="60"
                viewBox="0 0 60 60"
              >
                <circle
                  class="error-circle"
                  cx="30"
                  cy="30"
                  r="25"
                  fill="none"
                  stroke="#EF4444"
                  stroke-width="3"
                />
                <path
                  class="error-line1"
                  d="M20 20 L40 40"
                  stroke="#EF4444"
                  stroke-width="3"
                  stroke-linecap="round"
                />
                <path
                  class="error-line2"
                  d="M40 20 L20 40"
                  stroke="#EF4444"
                  stroke-width="3"
                  stroke-linecap="round"
                />
              </svg>
            </div>
          </div>

          <!-- Action Buttons for Error States -->
          <div v-if="currentStatus === 'error'" class="action-buttons mt-3">
            
            <B_Button
              type="primary"
              size="lg"
              :is-box="false"
              :fullwidth="true"
              leftIcon="login"
              @click="
                AuthService.loginWithProvider(
                  provider as ProviderLoginTypes,
                  role as RoleShortened
                )
              "
            >
              Try Again
            </B_Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;

.auth-wrapper {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

.auth-card {
  background: white;
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.logo-container {
  margin-bottom: 2rem;
}

.animation-container {
  margin: 2rem 0;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-title {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.status-message {
  margin-bottom: 2rem;
  color: #6b7280;
  line-height: 1.5;
}

// Animation Styles
.loading-animation {
  // Using FfDots component
}

.success-animation {
  .checkmark {
    animation: successBounce 0.6s ease-in-out;
  }

  .checkmark-circle {
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    animation: drawCircle 0.6s ease-in-out forwards;
  }

  .checkmark-check {
    stroke-dasharray: 30;
    stroke-dashoffset: 30;
    animation: drawCheck 0.4s ease-in-out 0.6s forwards;
  }
}

.error-animation {
  .error-cross {
    animation: errorShake 0.6s ease-in-out;
  }

  .error-circle {
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    animation: drawCircle 0.6s ease-in-out forwards;
  }

  .error-line1,
  .error-line2 {
    stroke-dasharray: 28;
    stroke-dashoffset: 28;
    animation: drawLine 0.4s ease-in-out 0.6s forwards;
  }

  .error-line2 {
    animation-delay: 0.8s;
  }
}

// Keyframe Animations
@keyframes successBounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes errorShake {
  0% {
    transform: translateX(0);
  }
  15% {
    transform: translateX(-10px);
  }
  30% {
    transform: translateX(10px);
  }
  45% {
    transform: translateX(-10px);
  }
  60% {
    transform: translateX(10px);
  }
  75% {
    transform: translateX(-5px);
  }
  90% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes drawCircle {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes drawCheck {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes drawLine {
  to {
    stroke-dashoffset: 0;
  }
}

// Action Buttons
.action-buttons {
  margin-top: 2rem;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #2563eb;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .auth-wrapper {
    padding: 1rem;
  }

  .auth-card {
    padding: 2rem 1.5rem;
  }
}
</style>
