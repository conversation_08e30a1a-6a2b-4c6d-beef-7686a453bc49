<template>
  <div class="resume-container">
    <div class="resume-header">
      <div class="header-content">
        <header class="header-actions">
          <B_Button
            class="dropdown-button"
            type="secondary"
            size="md"
            :is-box="false"
            right-icon="chevronDown"
          >
            <span> + </span>
          </B_Button>
          <B_Button
            type="secondary"
            size="md"
            :is-box="true"
            leftIcon="settings"
            to="/user/settings"
          />

          <B_Button
            type="secondary"
            size="md"
            :is-box="true"
            leftIcon="logout"
            @click="logoutUser"
          >
          </B_Button>
          <div class="user-avatar">
            <!-- <img src="/api/placeholder/32/32" alt="User" class="avatar-img" /> -->
            <B_Avatar size="sm" :user="authStore.user || undefined" />
          </div>
        </header>
      </div>
      <div class="resume-main">
        <div>
          <h4>Make your Resume Ready for Anything</h4>
          <p>
            Upload, tweak, and tailor your CV to fit your dream role — powered
            by AI magic.
          </p>
        </div>
        <div class="checkbox-group">
          <input-checkbox
            title="Upload CV"
            description="Charge an ongoing fee"
            right-icon="Checkbox"
            :is-route="true"
            to="/user/resume/upload"
            :model-value="selectedOption === 'upload'"
            @change="handleCheckboxChange('upload', $event)"
          />
          <input-checkbox
            title="Create New from Scratch"
            description="Allow access for free"
            :is-route="true"
            to="/user/resume/create"
            :model-value="selectedOption === 'create'"
            @change="handleCheckboxChange('create', $event)"
          />
        </div>
        <RouterView />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useAuthStore } from "@/stores/auth.store";
import { logoutUser } from "@/utils/helpers";
import B_Button from "@/components/talentflow/button.vue";
import B_Avatar from "@/components/ff/avatar.vue";
import InputCheckbox from "@/components/talentflow/input-checkbox.vue";

const authStore = useAuthStore();

// State management for radio-like behavior
const selectedOption = ref<string | null>(null);

// Handle checkbox selection - only one can be selected at a time
const handleCheckboxChange = (option: string, isChecked: boolean) => {
  if (isChecked) {
    selectedOption.value = option;
  } else {
    selectedOption.value = null;
  }
};
</script>

<style lang="scss" scoped>
@use "../../../assets/scss/mixin" as *;

.resume-header {
  padding: 1rem 0;
  .resume-main {
    margin-top: 88px !important;
    flex: 1;
    display: flex;
    flex-direction: column;

    margin: 0 auto;
    width: 100%;
    margin: 0 auto;
    gap: 39px;

    max-width: 656px;
    margin: 0 auto;
    box-sizing: border-box;
    @include respond-below(sm) {
      max-width: 100%;
      padding: 10px;
      flex-direction: column;
      padding: 20px;
    }
    .checkbox-group {
      display: flex;

      gap: 16px;
    }
  }
  .header-content {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      span {
        font-size: 1.2rem;
        font-weight: 400;
        color: #1f2937;
        line-height: 0;
      }

      .user-avatar {
        order: 999; // Ensure avatar appears last
      }
    }
  }
}
</style>
