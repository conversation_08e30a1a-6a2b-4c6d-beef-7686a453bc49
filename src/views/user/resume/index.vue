<template>
  <div class="resume-container">
    <div class="resume-header">
      <div class="header-content">
        <header class="header-actions">
          <B_Button
            class="dropdown-button"
            type="secondary"
            size="md"
            :is-box="false"
            right-icon="chevronDown"
          >
            <span> + </span>
          </B_Button>
          <B_Button
            type="secondary"
            size="md"
            :is-box="true"
            leftIcon="settings"
            to="/user/settings"
          />

          <B_Button
            type="secondary"
            size="md"
            :is-box="true"
            leftIcon="logout"
            @click="logoutUser"
          >
          </B_Button>
          <div class="user-avatar">
            <!-- <img src="/api/placeholder/32/32" alt="User" class="avatar-img" /> -->
            <B_Avatar size="sm" :user="authStore.user || undefined" />
          </div>
        </header>
      </div>
      <div class="resume-main">
        <!-- Initial Screen - Show when no upload is completed -->
        <div v-if="!isUploadCompleted" class="initial-screen">
          <div>
            <h4>Make your Resume Ready for Anything</h4>
            <p>
              Upload, tweak, and tailor your CV to fit your dream role — powered
              by AI magic.
            </p>
          </div>
          <div class="checkbox-group">
            <input-checkbox
              title="Upload CV"
              description="Charge an ongoing fee"
              right-icon="Checkbox"
              :is-route="true"
              to="/user/resume/upload"
              :model-value="selectedOption === 'upload'"
              @change="handleCheckboxChange('upload', $event)"
            />
            <input-checkbox
              title="Create New from Scratch"
              description="Allow access for free"
              :is-route="true"
              to="/user/resume/create"
              :model-value="selectedOption === 'create'"
              @change="handleCheckboxChange('create', $event)"
            />
          </div>
          <RouterView />
        </div>

        <!-- Post-Upload Screen - Show after upload is completed -->
        <div v-else class="post-upload-screen">
          <div class="upload-success">
            <h4>Great! Your Resume is Uploaded</h4>
            <p>
              We've successfully processed your resume. Now let's enhance it to
              match your dream role.
            </p>
          </div>

          <div class="uploaded-files">
            <h5>Uploaded Files:</h5>
            <div
              v-for="file in uploadedFiles"
              :key="file.name"
              class="file-item"
            >
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size"
                >({{ Math.round(file.size / 1024) }}KB)</span
              >
            </div>
          </div>

          <div class="next-steps">
            <h5>What would you like to do next?</h5>
            <div class="action-buttons">
              <B_Button type="primary" size="lg">
                Enhance Resume with AI
              </B_Button>
              <B_Button type="secondary" size="lg"> Preview Resume </B_Button>
              <B_Button type="secondary" size="md" @click="resetUpload">
                Upload Different Resume
              </B_Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide } from "vue";
import { useAuthStore } from "@/stores/auth.store";
import { logoutUser } from "@/utils/helpers";
import B_Button from "@/components/talentflow/button.vue";
import B_Avatar from "@/components/ff/avatar.vue";
import InputCheckbox from "@/components/talentflow/input-checkbox.vue";

const authStore = useAuthStore();

// State management for radio-like behavior
const selectedOption = ref<string | null>(null);

// State management for upload completion
const isUploadCompleted = ref(false);
const uploadedFiles = ref<any[]>([]);

// Handle checkbox selection - only one can be selected at a time
const handleCheckboxChange = (option: string, isChecked: boolean) => {
  if (isChecked) {
    selectedOption.value = option;
  } else {
    selectedOption.value = null;
  }
};

// Handle upload completion from child components
const handleUploadComplete = (files: any[]) => {
  isUploadCompleted.value = true;
  uploadedFiles.value = files;
  console.log("Upload completed with files:", files);
};

// Reset upload state to go back to initial screen
const resetUpload = () => {
  isUploadCompleted.value = false;
  uploadedFiles.value = [];
  selectedOption.value = null;
};

// Provide the upload completion handler to child components
provide("onUploadComplete", handleUploadComplete);
</script>

<style lang="scss" scoped>
@use "../../../assets/scss/mixin" as *;

.resume-header {
  padding: 1rem 0;
  .resume-main {
    margin-top: 88px !important;
    flex: 1;
    display: flex;
    flex-direction: column;

    margin: 0 auto;
    width: 100%;
    margin: 0 auto;
    gap: 39px;

    max-width: 656px;
    margin: 0 auto;
    box-sizing: border-box;
    @include respond-below(sm) {
      max-width: 100%;
      padding: 10px;
      flex-direction: column;
      padding: 20px;
    }
    .checkbox-group {
      display: flex;
      gap: 16px;
    }

    // Post-upload screen styles
    .post-upload-screen {
      display: flex;
      flex-direction: column;
      gap: 2rem;

      .upload-success {
        text-align: center;

        h4 {
          color: #059669;
          margin-bottom: 0.5rem;
        }

        p {
          color: #6b7280;
        }
      }

      .uploaded-files {
        background: #f9fafb;
        border-radius: 8px;
        padding: 1.5rem;

        h5 {
          margin: 0 0 1rem 0;
          color: #374151;
          font-size: 1rem;
          font-weight: 600;
        }

        .file-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 0;
          border-bottom: 1px solid #e5e7eb;

          &:last-child {
            border-bottom: none;
          }

          .file-name {
            font-weight: 500;
            color: #374151;
          }

          .file-size {
            color: #6b7280;
            font-size: 0.875rem;
          }
        }
      }

      .next-steps {
        h5 {
          margin: 0 0 1.5rem 0;
          color: #374151;
          font-size: 1.1rem;
          font-weight: 600;
          text-align: center;
        }

        .action-buttons {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          align-items: center;

          @include respond-above(sm) {
            flex-direction: row;
            justify-content: center;
            gap: 1rem;
          }
        }
      }
    }
  }
  .header-content {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      span {
        font-size: 1.2rem;
        font-weight: 400;
        color: #1f2937;
        line-height: 0;
      }

      .user-avatar {
        order: 999; // Ensure avatar appears last
      }
    }
  }
}
</style>
