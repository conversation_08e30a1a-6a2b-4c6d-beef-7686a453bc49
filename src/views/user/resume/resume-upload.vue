<template>
  <div>
    <FF_uploader
      :acceptTypes="'image/*'"
      :maxFileSize="10485760"
      :maxFiles="1"
      :showProgress="true"
      :uploadAction="'/api/upload'"
      :uploadAutomatically="true"
    />
  </div>
</template>

<script setup lang="ts">
import FF_uploader from "@/components/talentflow/upload-v2.vue";
</script>

<style scoped lang="scss">
@use "../../../assets/scss/mixin" as *;
</style>
