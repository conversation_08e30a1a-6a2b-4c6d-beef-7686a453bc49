<template>
  <div class="enhance-container">
    <div class="enhance-header">
      <h4>Make your Resume Ready for Anything</h4>
      <p>Upload, tweak, and tailor your CV to fit your dream role — powered by AI magic.</p>
    </div>

    <div class="enhance-options">
      <input-checkbox
        title="Tweak My Existing Resume"
        description="Freshen up look, update info, add sections"
        right-icon="Checkbox"
        size="lg"
        v-model="selectedOption.tweak"
        @change="handleOptionChange('tweak', $event)"
      />
      
      <input-checkbox
        title="Target a New Role"
        description="Align resume with a specific job post"
        right-icon="Checkbox"
        size="lg"
        v-model="selectedOption.target"
        @change="handleOptionChange('target', $event)"
      />
    </div>

    <div class="job-url-section">
      <h5>Paste the job role or URL</h5>
      <div class="url-input-container">
        <div class="url-input">
          <span class="url-icon">🔗</span>
          <input 
            type="text" 
            placeholder="https://" 
            v-model="jobUrl"
            class="url-field"
          />
          <button 
            class="fetch-btn"
            :class="{ loading: isFetching }"
            @click="fetchJobDetails"
            :disabled="!jobUrl || isFetching"
          >
            {{ isFetching ? 'Fetching...' : 'Fetch' }}
          </button>
        </div>
        <p class="url-help">Get the role requirements by providing the url</p>
      </div>
    </div>

    <div class="divider">
      <span>or</span>
    </div>

    <div class="upload-section">
      <p class="upload-text">Please upload your resume and it will be analyzed to enhance your profile.</p>
      
      <div class="upload-area">
        <div class="upload-placeholder">
          <div class="upload-icon">📄</div>
          <h6>Upload a file containing job description</h6>
          <p>Drag & drop a file here or...</p>
          <B_Button type="secondary" size="sm" @click="triggerFileUpload">
            Browse files
          </B_Button>
          <input 
            ref="fileInput" 
            type="file" 
            style="display: none" 
            @change="handleFileUpload"
            accept=".pdf,.doc,.docx,.txt"
          />
        </div>
        <p class="file-info">Upload a PDF or WORD file below 5.00 MB.</p>
      </div>
    </div>

    <div class="continue-section">
      <B_Button 
        type="primary" 
        size="lg" 
        :disabled="!canContinue"
        @click="handleContinue"
      >
        Continue
      </B_Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import InputCheckbox from "@/components/talentflow/input-checkbox.vue";
import B_Button from "@/components/talentflow/button.vue";

const router = useRouter();

// State management
const selectedOption = ref({
  tweak: false,
  target: false,
});

const jobUrl = ref("");
const isFetching = ref(false);
const uploadedFile = ref<File | null>(null);
const fileInput = ref<HTMLInputElement>();

// Handle option selection (radio-like behavior)
const handleOptionChange = (option: 'tweak' | 'target', isChecked: boolean) => {
  // Reset all options first
  selectedOption.value.tweak = false;
  selectedOption.value.target = false;
  
  // Set the selected option
  if (isChecked) {
    selectedOption.value[option] = true;
  }
};

// Computed property to check if user can continue
const canContinue = computed(() => {
  const hasSelectedOption = selectedOption.value.tweak || selectedOption.value.target;
  const hasJobInfo = jobUrl.value.trim() || uploadedFile.value;
  return hasSelectedOption && hasJobInfo;
});

// Fetch job details from URL
const fetchJobDetails = async () => {
  if (!jobUrl.value) return;
  
  isFetching.value = true;
  try {
    // TODO: Implement actual API call
    console.log("Fetching job details from:", jobUrl.value);
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
    console.log("Job details fetched successfully");
  } catch (error) {
    console.error("Error fetching job details:", error);
  } finally {
    isFetching.value = false;
  }
};

// Handle file upload
const triggerFileUpload = () => {
  fileInput.value?.click();
};

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    uploadedFile.value = target.files[0];
    console.log("File uploaded:", uploadedFile.value.name);
  }
};

// Handle continue action
const handleContinue = () => {
  if (!canContinue.value) return;
  
  const selectedType = selectedOption.value.tweak ? 'tweak' : 'target';
  const jobInfo = jobUrl.value || uploadedFile.value?.name;
  
  console.log("Continuing with:", { selectedType, jobInfo });
  
  // TODO: Navigate to next step or process the data
  // For now, just log the action
  alert(`Processing ${selectedType} option with job info: ${jobInfo}`);
};
</script>

<style scoped lang="scss">
@use "../../../assets/scss/mixin" as *;

.enhance-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 656px;
  margin: 0 auto;
  padding: 0 1rem;

  @include respond-below(sm) {
    padding: 0 0.5rem;
    gap: 1.5rem;
  }
}

.enhance-header {
  text-align: center;
  
  h4 {
    margin: 0 0 0.5rem 0;
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    color: #6b7280;
    line-height: 1.5;
  }
}

.enhance-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  @include respond-above(sm) {
    flex-direction: row;
    gap: 1rem;
  }
}

.job-url-section {
  h5 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
  }
}

.url-input-container {
  .url-input {
    display: flex;
    align-items: center;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 0.75rem;
    gap: 0.5rem;
    background: white;
    
    .url-icon {
      font-size: 1.2rem;
      color: #6b7280;
    }
    
    .url-field {
      flex: 1;
      border: none;
      outline: none;
      font-size: 0.875rem;
      
      &::placeholder {
        color: #9ca3af;
      }
    }
    
    .fetch-btn {
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      
      &:hover:not(:disabled) {
        background: #2563eb;
      }
      
      &:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }
      
      &.loading {
        background: #f59e0b;
      }
    }
  }
  
  .url-help {
    margin: 0.5rem 0 0 0;
    font-size: 0.875rem;
    color: #6b7280;
  }
}

.divider {
  text-align: center;
  position: relative;
  margin: 1rem 0;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
  }
  
  span {
    background: white;
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.875rem;
  }
}

.upload-section {
  .upload-text {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 0.875rem;
  }
}

.upload-area {
  .upload-placeholder {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background: #fafafa;
    
    .upload-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
    
    h6 {
      margin: 0 0 0.5rem 0;
      color: #374151;
      font-size: 1rem;
      font-weight: 600;
    }
    
    p {
      margin: 0 0 1rem 0;
      color: #6b7280;
      font-size: 0.875rem;
    }
  }
  
  .file-info {
    margin: 0.5rem 0 0 0;
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
  }
}

.continue-section {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}
</style>
