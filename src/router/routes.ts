import type { RouteRecordRaw } from "vue-router";
import { useAuthStore } from "@/stores/auth.store";

export const routes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/auth/login",
  },
  {
    path: "/auth",
    component: () => import("@layouts/AuthLayout.vue"),
    meta: { requiresAuth: false },
    children: [
      {
        path: "login",
        name: "login",
        component: () => import("@views/auth/Login.vue"),
      },
      {
        path: "signup",
        name: "signup",
        component: () => import("@views/auth/SignUp.vue"),
      },
      {
        path: "reset-password",
        name: "reset-password",
        component: () => import("@/views/auth/Reset-password.vue"),
      },
      {
        path: "set-new-password",
        name: "set-new-password",
        component: () => import("@/views/auth/SetNewPassword.vue"),
      },
    ],
  },
  {
    path: "/cb",
    component: () => import("@layouts/CbLayout.vue"),
    meta: { requiresAuth: false },
    children: [
      {
        path: "login",
        name: "cb-login",
        component: () => import("@views/cb/cb-login.vue"),
      },
      // {
      //   path: "signup",
      //   name: "signup",
      //   component: () => import("@views/cb/SignUp.vue"),
      // },
    ],
  },
  {
    path: "/user",
    meta: { requiresAuth: true },
    beforeEnter: (to, from, next) => {
      // const authStore = useAuthStore();

      // if (!authStore.user) {
      //   // Not logged in
      //   return next({ name: "login" });
      // }

      // if (authStore.user.role !== "TENANT") {
      //   // Logged in but not an TENANT
      //   return next({ name: "unauthorized" }); // Make sure you define this route
      // }

      next(); // All good
    },
    children: [
      {
        path: "onboarding",
        name: "onboarding",
        component: () => import("@views/user/onboarding.vue"),
      },
      {
        path: "dashboard",
        name: "dashboard",
        component: () => import("../views/user/dashboard/Index.vue"),
      },
    ],
  },
  {
    path: "/user/resume",
    component: () => import("@layouts/UserLayout.vue"),
    meta: { requiresAuth: true },

    children: [
      {
        path: "",
        name: "resume",
        component: () => import("@/views/user/resume/index.vue"),
        children: [
          {
            path: "upload",
            name: "resume.upload",
            component: () => import("@/views/user/resume/resume-upload.vue"),
          },
          {
            path: "create",
            name: "resume.create",
            component: () => import("@/views/user/resume/resume-create.vue"),
          },
        ],
      },
      // Add more resume-related routes here if needed
      // {
      //   path: "edit",
      //   name: "resume.edit",
      //   component: () => import("../views/user/resume/edit.vue"),
      // },
      // {
      //   path: "preview",
      //   name: "resume.preview",
      //   component: () => import("../views/user/resume/preview.vue"),
      // },
    ],
  },
  {
    path: "/admin",
    component: () => import("@layouts/AdminLayout.vue"),
    meta: { requiresAuth: true },
    beforeEnter: (to, from, next) => {
      // const authStore = useAuthStore();

      // if (!authStore.user) {
      //   // Not logged in
      //   return next({ name: "login" });
      // }

      // if (authStore.user.role !== "ADMIN") {
      //   // Logged in but not an admin
      //   return next({ name: "unauthorized" }); // Make sure you define this route
      // }

      next(); // All good
    },
    children: [
      // {
      //   path: "cron-jobs",
      //   name: "cron_jobs",
      //   component: () => import("@views/admin/cron-jobs.vue"),
      // },
    ],
  },
];
