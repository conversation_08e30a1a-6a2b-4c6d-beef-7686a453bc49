<template>
  <div :class="`ff_status ${selectedStyle} ${selectedSize}`">
    <template v-if="icon">
      <ff-icon :icon="icon" size="18" color="inherit"></ff-icon>
    </template>
    <template v-else-if="hasDot">
      <span class="ff_status-dot"></span>
    </template>
    {{ text }}
  </div>
</template>

<script setup lang="ts">
interface Props {
  text?: string;
  type: string;
  size?: string;
  icon?: string;
  hasDot?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  text: "",
  type: "primary",
  size: "md",
  hasDot: false,
});

const styles = {
  none: "",
  primary: " ff_status--primary",
  purple: " ff_status--purple",
  error: " ff_status--error",
  idle: " ff_status--idle",
};
const sizes = {
  lg: "ff_status-large",
  md: "ff_status-medium",
  sm: "ff_status-small",
};

const selectedStyle = computed(() => styles[props.type]);
const selectedSize = computed(() => sizes[props.size] || sizes.lg);
</script>

<style lang="scss" scoped>
.ff_status {
  display: inline-flex;

  align-items: center;
  align-self: stretch;
  padding: 1px 9px;
  border-radius: 9999px;
  gap: 6px;
  border: 1px solid #d9d6fe;
  background: #f4f3ff;
  justify-content: center;
  font-family: var(--font-1);
  font-style: normal;
  font-weight: 800;
  font-size: 14px;
  text-transform: uppercase;
  .ff_status-dot {
    width: 5px;
    height: 5px;
    background: #000;
    border-radius: 50px;
  }
  .ff-icon {
    font-size: 14px;
  }
  &.ff_status--purple {
    border-color: #d9d6fe;
    background: #f4f3ff;
    color: #5925dc;
    .ff_status-dot {
      background: #5925dc;
    }
  }
  &.ff_status--primary {
    border-color: #abefc6;
    background: #ecfdf3;
    color: #067647;
    .ff_status-dot {
      background: #067647;
    }
  }
  &.ff_status--error {
    border-color: #fecdca;
    background: #fef3f2;
    color: #b42318;
    .ff_status-dot {
      background: #b42318;
    }
  }
  &.ff_status--idle {
    border-color: #f0f0f0;
    background: #f9f9f9;
    color: #667085;
    .ff_status-dot {
      background: #667085;
    }
  }
}
</style>
