<template>
  <div class="ff_popover">
    <!-- User -->
    <div :class="`${triggerClass} ${placementClass} inline-block`">
      <div class="hs-tooltip-toggle">
        <!-- User Content -->
        <div class="ff_popover_content">
          <!-- Popover Trigger Content> -->
          <slot name="trigger_content"></slot>
        </div>
        <!-- End User Content -->

        <!-- Popover Content -->
        <div
          class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-10 max-w-sm w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:top-0 after:-start-4 after:w-4 after:h-full"
          role="tooltip"
        >
          <div class="ff_popover_header">
            <slot name="popover_header"> </slot>
          </div>

          <div class="ff_popover_body">
            <slot name="popover_body"> </slot>
          </div>

          <div class="ff_popover_footer">
            <slot name="popover_footer"> </slot>
          </div>
        </div>
        <!-- End Popover Content -->
      </div>
    </div>
    <!-- End User -->
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  direction: string;
  trigger: string;
}>();

const triggerClass = computed(() => {
  if (props.trigger == "hover") {
    return "hs-tooltip [--trigger:hover]";
  } else if (props.trigger == "click") {
    return "hs-tooltip [--trigger:focus]";
  }
  return "hs-tooltip [--trigger:focus]";
});

const placementClass = computed(() => {
  if (props.direction == "top") {
    return "sm:[--placement:top]";
  } else if (props.direction == "bottom") {
    return "sm:[--placement:bottom]";
  } else if (props.direction == "left") {
    return "[--placement:top] sm:[--placement:left]";
  } else if (props.direction == "right") {
    return "[--placement:top] sm:[--placement:right]";
  }
  return "[--placement:bottom] sm:[--placement:bottom]";
});
</script>

<style scoped></style>
