<template>
  <template v-if="isWhite">
    <svg
      :width="width"
      height="48"
      viewBox="0 0 86 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.6967 4.04437C18.8006 4.09269 22.2709 5.53123 25.1549 8.34699C26.1156 9.27628 27.0953 10.2056 28.0182 11.1813C28.4399 11.6255 28.6744 11.62 29.1132 11.1813C32.139 8.16175 35.1883 5.15952 38.2608 2.17463C39.0908 1.34798 40.1561 0.787847 41.3164 0.568036C42.4767 0.348226 43.6776 0.479054 44.7607 0.943284C45.8439 1.40751 46.7585 2.18336 47.3841 3.16858C48.0097 4.15381 48.3168 5.30218 48.2651 6.46238C48.2217 7.90206 47.6155 9.26994 46.5725 10.2818C40.7376 16.0236 34.9052 21.7647 29.0753 27.5053C28.5874 27.9885 28.3548 27.8249 27.969 27.4421C24.2585 23.7757 20.5392 20.1205 16.811 16.4764C15.1714 14.8762 12.6712 15.4319 11.9545 17.534C11.7677 18.0166 11.7312 18.543 11.8498 19.0461C11.9683 19.5492 12.2365 20.0062 12.6202 20.359C17.3078 24.9894 22.0086 29.6086 26.7227 34.2166C27.8196 35.2872 29.3704 35.2314 30.5259 34.1144C32.0785 32.6275 33.5896 31.083 35.1252 29.5702C44.329 20.4953 53.5378 11.4167 62.7516 2.33447C64.0886 1.01302 65.4919 -0.0110613 67.4795 9.02086e-05C69.9059 0.0112417 71.9654 1.28623 72.8732 3.38085C73.3433 4.43589 73.476 5.60642 73.2537 6.73728C73.0315 7.86814 72.4648 8.90592 71.6288 9.71305C67.3169 13.9704 62.9955 18.2192 58.6647 22.4592L51.0452 29.9474C50.1828 30.795 49.9332 31.806 50.3114 32.9212C50.6802 34.0066 51.4991 34.6515 52.6565 34.8262C53.7477 34.9916 54.5911 34.5214 55.3419 33.7817C59.5681 29.6148 63.8005 25.4534 68.0393 21.2976C70.4676 18.9112 72.8952 16.5248 75.3222 14.1383C77.1416 12.3429 79.2937 11.7482 81.7088 12.6273C84.1238 13.5064 85.3493 15.3297 85.4911 17.7979C85.5819 19.4018 84.9729 20.8237 83.8155 21.9964C78.5757 27.3132 73.3371 32.6288 68.0998 37.9431C66.0195 40.0526 63.9563 42.1844 61.8438 44.2623C57.3277 48.7229 50.4003 48.5649 46.0298 43.963C44.7627 42.6286 43.437 41.348 42.1774 40.008C41.7443 39.547 41.489 39.5898 41.0673 40.0191C39.5544 41.5524 38.0036 43.0505 36.4661 44.5596C31.9991 48.9403 25.6126 49.2172 21.0719 44.9313C15.1941 39.3723 9.39572 33.7204 3.80728 27.8826C-0.063961 23.8383 -0.920665 18.8424 0.953494 13.6198C2.8182 8.43434 6.6762 5.33051 12.1984 4.2581C13.0419 4.09083 13.8948 4.11499 14.6967 4.04437Z"
        fill="#fff"
      />
    </svg>
  </template>
  <template v-else-if="!isWhite && !isCompany">
    <svg
      :width="width"
      height="48"
      viewBox="0 0 86 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.6967 4.04437C18.8006 4.09269 22.2709 5.53123 25.1549 8.34699C26.1156 9.27628 27.0953 10.2056 28.0182 11.1813C28.4399 11.6255 28.6744 11.62 29.1132 11.1813C32.139 8.16175 35.1883 5.15952 38.2608 2.17463C39.0908 1.34798 40.1561 0.787847 41.3164 0.568036C42.4767 0.348226 43.6776 0.479054 44.7607 0.943284C45.8439 1.40751 46.7585 2.18336 47.3841 3.16858C48.0097 4.15381 48.3168 5.30218 48.2651 6.46238C48.2217 7.90206 47.6155 9.26994 46.5725 10.2818C40.7376 16.0236 34.9052 21.7647 29.0753 27.5053C28.5874 27.9885 28.3548 27.8249 27.969 27.4421C24.2585 23.7757 20.5392 20.1205 16.811 16.4764C15.1714 14.8762 12.6712 15.4319 11.9545 17.534C11.7677 18.0166 11.7312 18.543 11.8498 19.0461C11.9683 19.5492 12.2365 20.0062 12.6202 20.359C17.3078 24.9894 22.0086 29.6086 26.7227 34.2166C27.8196 35.2872 29.3704 35.2314 30.5259 34.1144C32.0785 32.6275 33.5896 31.083 35.1252 29.5702C44.329 20.4953 53.5378 11.4167 62.7516 2.33447C64.0886 1.01302 65.4919 -0.0110613 67.4795 9.02086e-05C69.9059 0.0112417 71.9654 1.28623 72.8732 3.38085C73.3433 4.43589 73.476 5.60642 73.2537 6.73728C73.0315 7.86814 72.4648 8.90592 71.6288 9.71305C67.3169 13.9704 62.9955 18.2192 58.6647 22.4592L51.0452 29.9474C50.1828 30.795 49.9332 31.806 50.3114 32.9212C50.6802 34.0066 51.4991 34.6515 52.6565 34.8262C53.7477 34.9916 54.5911 34.5214 55.3419 33.7817C59.5681 29.6148 63.8005 25.4534 68.0393 21.2976C70.4676 18.9112 72.8952 16.5248 75.3222 14.1383C77.1416 12.3429 79.2937 11.7482 81.7088 12.6273C84.1238 13.5064 85.3493 15.3297 85.4911 17.7979C85.5819 19.4018 84.9729 20.8237 83.8155 21.9964C78.5757 27.3132 73.3371 32.6288 68.0998 37.9431C66.0195 40.0526 63.9563 42.1844 61.8438 44.2623C57.3277 48.7229 50.4003 48.5649 46.0298 43.963C44.7627 42.6286 43.437 41.348 42.1774 40.008C41.7443 39.547 41.489 39.5898 41.0673 40.0191C39.5544 41.5524 38.0036 43.0505 36.4661 44.5596C31.9991 48.9403 25.6126 49.2172 21.0719 44.9313C15.1941 39.3723 9.39572 33.7204 3.80728 27.8826C-0.063961 23.8383 -0.920665 18.8424 0.953494 13.6198C2.8182 8.43434 6.6762 5.33051 12.1984 4.2581C13.0419 4.09083 13.8948 4.11499 14.6967 4.04437Z"
        fill="#000"
      />
    </svg>
  </template>
  <template v-else>
    <svg
      :width="width"
      height="48"
      viewBox="0 0 86 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.6967 4.04437C18.8006 4.09269 22.2709 5.53123 25.1549 8.34699C26.1156 9.27628 27.0953 10.2056 28.0182 11.1813C28.4399 11.6255 28.6744 11.62 29.1132 11.1813C32.139 8.16175 35.1883 5.15952 38.2608 2.17463C39.0908 1.34798 40.1561 0.787847 41.3164 0.568036C42.4767 0.348226 43.6776 0.479054 44.7607 0.943284C45.8439 1.40751 46.7585 2.18336 47.3841 3.16858C48.0097 4.15381 48.3168 5.30218 48.2651 6.46238C48.2217 7.90206 47.6155 9.26994 46.5725 10.2818C40.7376 16.0236 34.9052 21.7647 29.0753 27.5053C28.5874 27.9885 28.3548 27.8249 27.969 27.4421C24.2585 23.7757 20.5392 20.1205 16.811 16.4764C15.1714 14.8762 12.6712 15.4319 11.9545 17.534C11.7677 18.0166 11.7312 18.543 11.8498 19.0461C11.9683 19.5492 12.2365 20.0062 12.6202 20.359C17.3078 24.9894 22.0086 29.6086 26.7227 34.2166C27.8196 35.2872 29.3704 35.2314 30.5259 34.1144C32.0785 32.6275 33.5896 31.083 35.1252 29.5702C44.329 20.4953 53.5378 11.4167 62.7516 2.33447C64.0886 1.01302 65.4919 -0.0110613 67.4795 9.02086e-05C69.9059 0.0112417 71.9654 1.28623 72.8732 3.38085C73.3433 4.43589 73.476 5.60642 73.2537 6.73728C73.0315 7.86814 72.4648 8.90592 71.6288 9.71305C67.3169 13.9704 62.9955 18.2192 58.6647 22.4592L51.0452 29.9474C50.1828 30.795 49.9332 31.806 50.3114 32.9212C50.6802 34.0066 51.4991 34.6515 52.6565 34.8262C53.7477 34.9916 54.5911 34.5214 55.3419 33.7817C59.5681 29.6148 63.8005 25.4534 68.0393 21.2976C70.4676 18.9112 72.8952 16.5248 75.3222 14.1383C77.1416 12.3429 79.2937 11.7482 81.7088 12.6273C84.1238 13.5064 85.3493 15.3297 85.4911 17.7979C85.5819 19.4018 84.9729 20.8237 83.8155 21.9964C78.5757 27.3132 73.3371 32.6288 68.0998 37.9431C66.0195 40.0526 63.9563 42.1844 61.8438 44.2623C57.3277 48.7229 50.4003 48.5649 46.0298 43.963C44.7627 42.6286 43.437 41.348 42.1774 40.008C41.7443 39.547 41.489 39.5898 41.0673 40.0191C39.5544 41.5524 38.0036 43.0505 36.4661 44.5596C31.9991 48.9403 25.6126 49.2172 21.0719 44.9313C15.1941 39.3723 9.39572 33.7204 3.80728 27.8826C-0.063961 23.8383 -0.920665 18.8424 0.953494 13.6198C2.8182 8.43434 6.6762 5.33051 12.1984 4.2581C13.0419 4.09083 13.8948 4.11499 14.6967 4.04437Z"
        fill="#247447"
      />
    </svg>
  </template>
</template>

<script setup lang="ts">
interface Props {
  isCompany?: boolean;
  isWhite?: boolean;
  width?: string;
}

// Use withDefaults to provide a default value for isCompany if it's not passed
const props = withDefaults(defineProps<Props>(), {
  isCompany: false, // Default value for isCompany
  isWhite: false, // Default value for isCompany
  width: "40px",
});
</script>
<style scoped></style>
