<template>
  <button
    :class="`${defaultStyle} ${selectedStyle} ${selectedSize} ${selectedSocial}`"
    :disabled="disabled"
    type="submit"
    @click="onClick"
  >
    <template v-if="isLoading">
      <ff-loader :variant="type" size="sm" />
    </template>
    <template v-else>
      <template v-if="isFacebook">
        <ff-icon icon="facebook"></ff-icon>
      </template>
      <template v-else>
        <ff-icon icon="google"></ff-icon>
      </template>
      <slot></slot>
    </template>
  </button>
</template>

<script setup lang="ts">

import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { RouteLocationRaw } from 'vue-router'

interface Props {
  type: string
  size?: string
  disabled?: boolean
  isLoading?: boolean
  isGoogle?: boolean
  isFacebook?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'none',
  size: 'md', // Your default size value
  disabled: false,
  isLoading: false,
  isGoogle: false,
  isFacebook: false
})

const emit = defineEmits(['click'])

const router = useRouter()

const defaultStyle = ref('btn')
const styles = {
  bordered: 'bordered'
}
const sizes = {
  lg: 'btn-large',
  md: 'btn-medium',
  sm: 'btn-small'
  // ... other sizes
}
const socials = {
  isFacebook: 'btn-facebook',
  isGoogle: 'btn-google'
  // ... other sizes
}

const selectedStyle = computed(() => styles[props.type])
const selectedSize = computed(() => sizes[props.size] || sizes.lg)
const selectedSocial = computed(() => socials[props.isGoogle] || socials.isFacebook)

const onClick = (event: Event) => {
  if (props.isButton) {
    emit('click')
  } else if (props.to) {
    router.push(props.to)
  } else {
    if (!props.href) {
      event.preventDefault()
      emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
/* Your styles here */
</style>
