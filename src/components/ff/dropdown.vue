<template>
  <div
    class="hs-dropdown hs-dropdown-example inline-flex"
    :class="{ '[--auto-close:inside]': hasCheckbox }"
  >
    <button
      id="hs-dropdown-example"
      type="button"
      class="hs-dropdown-toggle py-2 px-2 relative inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none"
    >
      <template v-if="showLoader">
        <ff-loader size="sm" />
      </template>
      <template v-else>
        <ff-icon icon="dots-horizontal"></ff-icon>
      </template>
    </button>
    <div
      class="hs-dropdown-menu relative transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 w-56 hidden z-10 mt-0 min-w-60 bg-white shadow-md rounded-lg p-2"
      aria-labelledby="hs-dropdown-example"
    >
      <slot name="dropdown"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { boolean } from "yup";

const props = defineProps({
  key: {
    type: String,
    required: false,
    default: "",
  },
  showLoader: false,
  hasCheckbox: {
    type: Boolean,
    default: false,
    required: false,
  },
});
</script>

<style scoped></style>
