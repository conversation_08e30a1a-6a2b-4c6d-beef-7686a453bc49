<script lang="ts" setup>
const props = defineProps({
  type: String,
});
</script>

<template>
  <template v-if="type == 'table'">
    <div class="">
      <div class="flex p-4 items-center">
        <div class="flex-shrink-0">
          <span class="size-6 block bg-gray-200 rounded-full"></span>
        </div>

        <div class="flex ms-4 gap-2 w-full">
          <p class="h-4 bg-gray-200 rounded-full" style="width: 40%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
        </div>
      </div>
    </div>
    <div class="">
      <div class="flex p-4 items-center">
        <div class="flex-shrink-0">
          <span class="size-6 block bg-gray-200 rounded-full"></span>
        </div>

        <div class="flex ms-4 gap-2 w-full">
          <p class="h-4 bg-gray-200 rounded-full" style="width: 40%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
        </div>
      </div>
    </div>
    <div class="">
      <div class="flex p-4 items-center">
        <div class="flex-shrink-0">
          <span class="size-6 block bg-gray-200 rounded-full"></span>
        </div>

        <div class="flex ms-4 gap-2 w-full">
          <p class="h-4 bg-gray-200 rounded-full" style="width: 40%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
        </div>
      </div>
    </div>
    <div class="">
      <div class="flex p-4 items-center">
        <div class="flex-shrink-0">
          <span class="size-6 block bg-gray-200 rounded-full"></span>
        </div>

        <div class="flex ms-4 gap-2 w-full">
          <p class="h-4 bg-gray-200 rounded-full" style="width: 40%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
        </div>
      </div>
    </div>
    <div class="">
      <div class="flex p-4 items-center">
        <div class="flex-shrink-0">
          <span class="size-6 block bg-gray-200 rounded-full"></span>
        </div>

        <div class="flex ms-4 gap-2 w-full">
          <p class="h-4 bg-gray-200 rounded-full" style="width: 40%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 20%"></p>
        </div>
      </div>
    </div>
  </template>
  <template v-if="type == 'reservation_list'">
    <div class="mt-2">SKELETON</div>
  </template>
  <template v-else-if="type == 'edit-vehicle-locations'">
    <div class="form_with_description">
      <div class="form_description">
        <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
        <p class="h-4 bg-gray-200 rounded-full" style="width: 50%"></p>
      </div>
      <div class="form_inputs">
        <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
        <p class="h-4 bg-gray-200 rounded-full mb-2" style="width: 50%"></p>

        <p class="h-20 bg-gray-200 rounded-lg" style="width: 100%"></p>
        <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
      </div>
    </div>
  </template>
  <template v-else-if="type == 'vehicle-preview'">
    <p
      class="bg-gray-200 rounded-md mb-1 vehicle_preview_image"
      style="width: 100%"
    ></p>
    <p class="h-4 bg-gray-200 rounded-full" style="width: 50%"></p>
    <div class="flex gap-2">
      <p class="h-4 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
      <p class="h-4 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
      <p class="h-4 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
      <p class="h-4 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
      <p class="h-4 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
    </div>
    <div class="flex justify-between mt-2 gap-5">
      <p class="h-4 bg-gray-200 rounded-full" style="width: 100%"></p>
      <p class="h-4 bg-gray-200 rounded-full" style="width: 100%"></p>
    </div>
  </template>
  <template v-else-if="type == 'vehicleDetails'">
    <div class="">
      <div class="form_with_description">
        <div class="form_description">
          <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 50%"></p>
        </div>
        <div class="form_inputs">
          <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
          <p class="h-4 bg-gray-200 rounded-full mb-2" style="width: 50%"></p>

          <p class="h-20 bg-gray-200 rounded-lg" style="width: 100%"></p>
          <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        </div>
      </div>
      <div class="form_with_description">
        <div class="form_description">
          <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 50%"></p>
        </div>
        <div class="form_inputs">
          <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
          <p class="h-4 bg-gray-200 rounded-full mb-2" style="width: 50%"></p>

          <p class="h-20 bg-gray-200 rounded-lg" style="width: 100%"></p>
          <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        </div>
      </div>
      <div class="form_with_description">
        <div class="form_description">
          <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
          <p class="h-4 bg-gray-200 rounded-full" style="width: 50%"></p>
        </div>
        <div class="form_inputs">
          <p class="h-4 bg-gray-200 rounded-full mb-1" style="width: 80%"></p>
          <p class="h-4 bg-gray-200 rounded-full mb-2" style="width: 50%"></p>

          <p class="h-20 bg-gray-200 rounded-lg" style="width: 100%"></p>
          <p class="h-20 bg-gray-200 rounded-lg mt-2" style="width: 100%"></p>
        </div>
      </div>
    </div>
  </template>
  <template v-else-if="type == 'edit-vehicle-pricing'">
    <div>
      <div class="card mt-2">
        <p class="h-4 bg-gray-200 rounded-full" style="width: 40%"></p>
        <p class="h-14 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
      </div>
      <div class="card mt-3">
        <p
          class="h-4 bg-gray-200 rounded-full flex just-center items-center"
          style="width: 40%"
        ></p>
        <div class="flex gap-2">
          <p class="h-14 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
          <p class="h-14 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
          <p class="h-14 bg-gray-200 rounded-lg mt-1" style="width: 100%"></p>
        </div>
      </div>
    </div>
  </template>
</template>

<style lang="scss" scoped>
.vehicle_preview_image {
  min-height: 300px;
  max-height: 300px;
}
</style>
