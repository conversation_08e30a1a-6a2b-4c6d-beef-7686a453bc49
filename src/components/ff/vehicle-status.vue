<template>
  <template v-if="status == 'available'">
    <ff-badge
      :text="t('vehicle.status.' + status)"
      type="success"
      :border="true"
    ></ff-badge>
  </template>
  <template v-if="status == 'rented'">
    <ff-badge
      :text="t('vehicle.status.' + status)"
      type="orange"
      :border="true"
    ></ff-badge>
  </template>

  <template v-else-if="status == 'inactive'">
    <ff-badge
      :text="t('vehicle.status.' + status)"
      type="danger"
      icon="close"
    ></ff-badge>
  </template>
  <template v-else-if="status == 'terminated'">
    <ff-chip
      :chip-content="t('vehicle.status.' + status)"
      type="danger"
      icon="close"
    ></ff-chip>
  </template>
  <template v-else-if="status == 'draft'">
    <ff-badge
      :text="t('vehicle.status.' + status)"
      type="danger"
      :border="true"
    ></ff-badge>
  </template>
  <template v-else-if="status == 'in_service'">
    <ff-badge
      :text="t('vehicle.status.' + status)"
      type="danger"
      :border="true"
    ></ff-badge>
  </template>
  <template v-else-if="status == 'archived'">
    <ff-badge
      :text="t('vehicle.status.' + status)"
      type="danger"
      :border="true"
    ></ff-badge>
  </template>
</template>

<script lang="ts" setup>
const props = defineProps({
  status: {
    type: String,
    required: true,
  },
});
const { t } = useI18n();
</script>

<style></style>
