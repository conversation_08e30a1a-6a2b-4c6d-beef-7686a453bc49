<template>
    <div :class="[extraClass]" class="widget">
      <div class="heading">
        <p class="title">{{ title }}</p>
        <ff-icon class="icon" icon="dots" fill="none" />
      </div>
      <div class="number">
        <h4>
          <!-- Assuming <vue-number> is a custom component for formatting numbers -->
          <vue-number v-model="formattedNumber" :options="number"></vue-number>
          {{ formattedTotal }}
        </h4>
        <div class="badge">
          <ff-icon class="icon" :icon="getIcon()" fill="none" />
          <p class="percentage" :style="{ color: getPercentageColor() }">{{ percentage }}%</p>
          <p>{{ $t('general.from_last_month') }}</p>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { directive as VNumber } from '@coders-tm/vue-number-format';

const props = defineProps({
  title: String,
  total: Number,
  percentage: Number,
  extraClass: String,
  type: String, // 'increase' or 'decrease'
});

const number = {
  decimal: '.',
  separator: ',',
  precision: 2,
  masked: false,
};

const formattedNumber = computed(() => {
  return price.value.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
});

const formattedTotal = computed(() => {
  return total.value.toLocaleString('en-US');
});

const price = ref(1234567.89); // Replace with your actual price value
const total = ref(props.total);

const getIcon = () => {
  return props.type === 'increase' ? 'increase' : 'decrease';
};

const getPercentageColor = () => {
  return props.type === 'increase' ? '#17B26A' : 'red';
};
</script>

<style lang="scss" scoped>
.widget {
  width: 336px;
  padding: 24px;
  // border: 1px solid black;
  border: 1px solid #EAECF0;

  .heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .number {
    display: flex;
    justify-content: space-between;

    h4 {
      font-size: 36px;
      font-family: var(--font-2);
      color: #101828;
      font-weight: 600;
    }

    .badge {
    display: flex;
    font-size: 14px !important;
    align-items: center;
    justify-content: space-between !important;

    p {
      font-size: 14px;
    }

    .percentage {
      margin: 0 7px 0 7px;
    }
  }
  }
}
</style>