<template>
  <div
    class="ff_badge items-center gap-x-2"
    @click="$emit('click')"
    :class="`${computedType} ${active ? 'active' : ''} ${
      bigger ? 'bigger' : ''
    } ${borderClass}`"
  >
    <template v-if="type || active">
      <div class="dot" :class="[type ? type : 'success']"></div>
    </template>

    {{ text }}
  </div>
</template>

<script lang="ts" setup>
const emits = defineEmits(["click"]);
const props = defineProps<{
  text: string;
  type: string;
  bigger?: boolean;
  active?: boolean;
  border?: boolean;
}>();

const computedType = computed(() => {
  if (!props.type) return "";
  return "ff_badge_" + props.type;
});

const borderClass = computed(() => {
  if (!props.border) return "";
  return "has_border";
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/variables.scss";
.ff_badge {
  background: #fff;
  // margin: 10px;
  display: inline-flex;
  position: relative;
  border-radius: 6px;
  padding: 5px 10px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: #000;
  font-family: var(--font-1);
  font-weight: 600;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  border: 1px solid transparent;
  &.has_border {
    border: 1px solid #eaecf0;
  }
  &.active {
    border: 1px solid #247447 !important;
    i {
      color: #247447 !important;
    }
  }
  &.bigger {
    padding: 12px 20px;
  }
  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    &.success {
      background: #3c9e19;
      // Blink
      animation: blink 1s linear infinite;
    }
    &.orange {
      background: #f48659;
      // Blink
      animation: blink 1s linear infinite;
    }
    &.danger {
      background: #b9201f;
      // Blink
      animation: blink 1s linear infinite;
    }
  }
}
</style>
