<template>
  <form @submit.prevent="onSubmit" class="b-form">
    <slot />
  </form>
</template>

<script setup lang="ts">
import { provide, watch, onMounted, nextTick, type Ref } from "vue";
import { useForm, type TypedSchema } from "vee-validate";
import type { GenericObject } from "vee-validate";

interface Props {
  validationSchema: TypedSchema<GenericObject, GenericObject>;
  initialValues?: Record<string, any>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  submit: [values: Record<string, any>];
  "validation-change": [isValid: boolean];
}>();

// Setup vee-validate form
const { handleSubmit, meta, validate, values } = useForm({
  validationSchema: props.validationSchema,
  initialValues: props.initialValues || {},
});

// Provide form context to child components
provide("formContext", {
  handleSubmit,
  meta,
  validate,
  values,
});

// Handle form submission
const onSubmit = handleSubmit((values) => {
  emit("submit", values);
});

// Watch for validation changes and emit to parent
watch(
  () => meta.value.valid,
  (isValid) => {
    emit("validation-change", isValid);
  },
  { immediate: true } // Emit initial validation state
);

// Trigger initial validation if there are initial values
onMounted(async () => {
  if (props.initialValues && Object.keys(props.initialValues).length > 0) {
    // Wait for child components to mount and register their fields
    await nextTick();

    // Check if any initial values are non-empty (indicating pre-filled data)
    const hasNonEmptyValues = Object.values(props.initialValues).some(
      (value) => value !== null && value !== undefined && value !== ""
    );

    if (hasNonEmptyValues) {
      // Trigger validation to check if the initial values are valid
      const result = await validate();
      // The watch above will automatically emit the validation state
    }
  }
});

// Expose methods for parent components
defineExpose({
  submit: onSubmit,
  validate,
  meta,
  values,
});
</script>

<style scoped lang="scss">
.b-form {
  // Form-specific styles can be added here if needed
  width: 100%;
}
</style>
