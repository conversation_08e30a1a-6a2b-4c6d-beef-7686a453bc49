<template>
  <div>
    <div class="ff_datepicker_container">
      <div class="ff_select_container">
        <label v-if="label">{{ label }}</label>
        <div class="ff_select_input" tabindex="0" @click="toggleDropdown">
          <div class="left_icon">
            <ff-icon icon="calendar" size="18px" color="inherit" />
          </div>
          <div class="ff_select_input_text">
            <template v-if="currentRange">
              <p>{{ currentRange }}</p>
            </template>
            <template v-else>
              <p class="placeholder">{{ placeholder }}</p>
            </template>
          </div>

          <div class="right_icon">
            <ff-icon
              icon="chevron-down"
              color="#667085"
              size="18px"
              v-if="!showDropdown"
            />
            <ff-icon
              icon="chevron-up"
              color="#667085"
              size="18px"
              v-if="showDropdown"
            />
          </div>
        </div>
        <div
          class="ff_dropdown_container"
          v-if="showDropdown"
          ref="dropdownContainer"
        >
          <div class="ff_dropdown_container_mobile">
            <div class="dropdown_container_mobile_message">
              <template v-if="label">
                <p>{{ label }}</p>
              </template>
              <template v-else>
                <p>{{ placeholder }}</p>
              </template>
            </div>
          </div>
          <div v-if="showDropdown" class="dropdown">
            <VDatePicker
              borderless
              transparent
              expanded
              color="sky-blue"
              v-model="currentRange"
              ref="calendar"
              :attributes="attrs"
              :is-range="true"
            >
              <template #footer>
                <div class="w-full px-4 pb-3">
                  <button
                    class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold w-full px-3 py-1 rounded-md"
                    @click="moveToday"
                  >
                    Today
                  </button>
                </div>
              </template>
            </VDatePicker>

            <!-- <div class="actions" v-if="viewMode === 'days'">
            <button @click="cancelSelection" class="btn_cancel">Cancel</button>
            <button @click="applySelection" class="btn_apply">Apply</button>
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <!-- <VCalendar
      v-model="currentRange"
      :is-range="true"
      :disabled-dates="disabledDates"
      @range-complete="addRange"
    /> -->
    <!-- <ul>
      <li v-for="(range, index) in dateRanges" :key="index">
        From: {{ range.start.toLocaleDateString() }}, To:
        {{ range.end.toLocaleDateString() }}
      </li>
    </ul> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps } from "vue";
import { parseISO, addDays } from "date-fns";

const props = defineProps({
  initialStart: String,
  initialEnd: String,
  label: String,
  placeholder: String,
  disabledDates: Object,
});
const calendar = ref(null);
function moveToday() {
  calendar.value.move(new Date());
}

const attrs = ref([
  {
    // This is a single attribute

    // An optional key can be used for retrieving this attribute later,
    // and will most likely be derived from your data object
    key: "calendar",
    // Attribute type definitions
    content: "red", // Boolean, String, Object
    highlight: true, // Boolean, String, Object
    dot: true, // Boolean, String, Object
    bar: true, // Boolean, String, Object
    // popover: { ... }, // Only objects allowed
    // Your custom data object for later access, if needed
    // customData: { ... },
    // We also need some dates to know where to display the attribute
    // We use a single date here, but it could also be an array of dates,
    //  a date range or a complex date pattern.
    dates: new Date(),
    // Think of `order` like `z-index`
    order: 0,

    calendarOptions: {
      type: "day",
      showWeekNumber: true,
      firstDayOfWeek: 1,
      isRange: true,
    },
  },
]);
const showDropdown = ref(false);
const dateRanges = ref([]);
const currentRange = ref({
  start: props.initialStart ? parseISO(props.initialStart) : new Date(),
  end: props.initialEnd ? parseISO(props.initialEnd) : addDays(new Date(), 10),
});

function toggleDropdown() {
  showDropdown.value = !showDropdown.value;
}

// Watch for changes in initial dates from the parent component
watch(
  () => props.initialStart,
  (newStart) => {
    if (newStart) {
      currentRange.value.start = parseISO(newStart);
    }
  }
);
watch(
  () => props.initialEnd,
  (newEnd) => {
    if (newEnd) {
      currentRange.value.end = parseISO(newEnd);
    }
  }
);

const addRange = (range) => {
  dateRanges.value.push({
    start: new Date(range.start),
    end: new Date(range.end),
  });
  // Automatically set up the next range starting the day after the last end date
  currentRange.value.start = addDays(range.end, 1);
  currentRange.value.end = addDays(range.end, 11);
};
</script>

<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";
.ff_datepicker_container {
  .ff_select_container {
    position: relative;
    label {
      font-family: var(--font-2);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      color: #000;

      line-height: 37px;
    }
    .ff_select_input {
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--Colors-Border-border-primary, #d0d5dd);
      background: var(--Colors-Background-bg-primary, #fff);

      /* Shadows/shadow-sm */
      box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
      display: flex;
      padding: 10px 14px;
      align-items: center;
      gap: var(--spacing-md, 8px);
      align-self: stretch;
      .left_icon {
        .ff-icon {
          svg {
            stroke: #667085;
          }
        }
      }
      .ff_select_input_text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        flex: 1 0 0;
        p {
          &.placeholder {
            color: #667085;
          }
        }
      }
      cursor: pointer;
      &:focus {
        border-radius: var(--radius-md, 8px);
        border: 1px solid var(--color--primary-lighter-v2);
        background: var(--Colors-Background-bg-primary, #fff);

        /* Focus rings/ring-brand-shadow-sm */
        box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05),
          0px 0px 0px 4px var(--color--primary-lighter);
      }
    }

    .ff_dropdown_backdrop {
      position: fixed;
      background: rgba(0, 0, 0, 0.15);
      height: 100%;
      width: 100%;
      z-index: 99;
      top: 0;
      bottom: 0;
      display: none;
      left: 0;
      @include respond-below(sm) {
        display: block;
      }
    }

    .ff_dropdown_container {
      display: flex;
      width: 100%;
      height: auto;
      align-items: flex-start;
      position: absolute;
      flex-direction: column;
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--Colors-Border-border-secondary, #eaecf0);
      background: #fff;

      /* Shadows/shadow-lg */
      box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
        0px 4px 6px -2px rgba(16, 24, 40, 0.03);
      z-index: 9;
      max-width: 430px;
      padding: 5px;
      min-width: 430px;
      .dropdown {
        width: 100%;
      }
      @include respond-below(sm) {
        position: fixed;
        bottom: 0;
        border-radius: 0;
        z-index: 999;
        max-width: initial;
        width: 100%;
        left: 0;
      }
      .ff_dropdown_container_mobile {
        display: none;
        width: 100%;
        @include respond-below(sm) {
          display: block;
        }
        .dropdown_container_mobile_message {
          padding-left: 20px;
          margin: 20px 0;
          p {
            font-size: 14px;
            color: #667085;
          }
          display: none;
          @include respond-below(sm) {
            display: block;
          }
        }
      }
    }
  }
}

.weekdays span {
  display: inline-block;
  width: 14.28%; /* 100% / 7 days */
  // width: 45px;
  text-align: center;
  font-weight: bold;
}

.day {
  display: inline-block;
  width: 14.28%; /* 100% / 7 days */
  text-align: center;
  /* Additional styling for day cells */
}

.header button {
  margin: 0 5px;
  /* Further button styling */
}

.weekdays span {
  display: inline-block;
  width: 14.28%; /* 100% / 7 days */
  text-align: center;
  font-weight: bold;
}

.day {
  display: inline-block;
  width: 14.28%; /* 100% / 7 days */
  text-align: center;
  /* Additional styling for day cells */
}
</style>
