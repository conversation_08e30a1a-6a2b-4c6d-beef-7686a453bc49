<template>
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.5 12H2.50667M5.83333 4H14.5M5.83333 8H14.5M5.83333 12H14.5M2.5 4H2.50667M2.5 8H2.50667"
      stroke="black"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</template>

<script setup lang="ts">
defineProps<{
  size?: number;
  className?: string;
}>();
</script>
<style scoped>
svg {
  color: black;
}
</style>
