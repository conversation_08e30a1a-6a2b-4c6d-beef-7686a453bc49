<template>
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4"
      y="4"
      width="16"
      height="16"
      rx="4"
      fill="currentColor"
      stroke="currentColor"
      stroke-width="1"
    />
    <path
      d="M8 12.2L10.5455 15C10.5455 15 13 11.5 16 9"
      stroke="white"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</template>

<script setup lang="ts">
interface Props {
  checked?: boolean;
  size?: number | string;
  value?: string | number;
  showValue?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  checked: false,
  size: 18,
  value: "",
  showValue: false,
});
</script>

<style scoped></style>
