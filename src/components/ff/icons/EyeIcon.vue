<template>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_87_5747)">
      <path
        d="M8.00033 2.66675C3.33366 2.66675 0.666992 8.00008 0.666992 8.00008C0.666992 8.00008 3.33366 13.3334 8.00033 13.3334C12.667 13.3334 15.3337 8.00008 15.3337 8.00008C15.3337 8.00008 12.667 2.66675 8.00033 2.66675Z"
        stroke="#717179"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_87_5747">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>

<script setup lang="ts">
defineProps<{
  size?: number;
  className?: string;
}>();
</script>
