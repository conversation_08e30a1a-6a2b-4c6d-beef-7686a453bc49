<template>
  <transition name="fade">
    <div class="ff-confirmation-dialog" v-if="isVisible">
      <div class="ff-confirmation-dialog-inner">
        <div class="modal-saving-animation" v-if="loading"></div>
        <div class="dialog-header" v-if="icon">
          <div class="header_pattern">
            <div class="header_icon">
              <ff-icon :icon="icon" color="inherit"></ff-icon>
            </div>
          </div>
          <div class="header_close_btn" @click="cancelAction">
            <ff-icon icon="close" size="24px" color="#98A2B3"></ff-icon>
          </div>
        </div>
        <div class="dialog-body">
          <p class="text-bold text-dark">{{ title }}</p>
          <p class="mt-1 text-medium">{{ message }}</p>
          <template v-if="extraDescription">
            <p class="mt-3 mb-3">
              {{ extraDescription }}
            </p>
          </template>
          <slot name="dialog_description" />
        </div>
        <div class="dialog-footer">
          <ff-button
            type="outlined"
            size="md"
            @click="switchButtonPlaces ? confirmAction() : cancelAction()"
          >
            <template v-if="switchButtonPlaces">
              {{ okButton }}
            </template>
            <template v-else>
              {{ cancelButton }}
            </template>
          </ff-button>

          <template v-if="extraButtonVisible">
            <ff-button type="outlined" size="lg" @click="noneAction()">
              {{ extraButtonTitle }}
            </ff-button>
          </template>
          <ff-button
            type="primary"
            size="md"
            :disabled="loading"
            @click="switchButtonPlaces ? cancelAction() : confirmAction()"
          >
            <template v-if="loading">
              <ff-loader size="sm" variant="error"></ff-loader>
            </template>
            <template v-else>
              {{ okButton }}
            </template>
          </ff-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  message?: string;
  okButton: string;
  icon: string;
  loading?: boolean;
  cancelButton: string;
  switchButtonPlaces?: boolean;
  extraButtonVisible?: boolean;
  extraButtonTitle?: string;
  extraDescription?: string;
  variant?: "primary" | "error";
}

const props = defineProps<Props>();
const emit = defineEmits(["extraButtonClicked"]);

const isVisible = ref(false);

function showConfirmation(): Promise<boolean> {
  isVisible.value = true;
  return new Promise((resolve) => {
    const resolvePromise = (result: boolean) => {
      isVisible.value = false;
      resolve(result);
    };
    // Bind the resolve function to actions
    confirmAction = () => resolvePromise(true);
    cancelAction = () => resolvePromise(false);
  });
}

// Bind these functions later in `showConfirmation`
let confirmAction = () => {};
let cancelAction = () => {};

function noneAction() {
  emit("extraButtonClicked");
  isVisible.value = false;
}

defineExpose({ showConfirmation });
</script>

<style lang="scss" scoped>
@import "../../assets/scss/variables.scss";

.ff-confirmation-dialog {
  background-color: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

  display: flex;
  z-index: 500000;

  .modal-saving-animation {
    top: 0;
    height: 9px;
    left: 0;
    &:before {
      background: var(--color--error);
    }
    &:after {
      background: var(--color--error);
    }
  }
  .ff-confirmation-dialog-inner {
    background: #fff;
    border-radius: 9px;
    box-shadow: 0px 20px 24px -4px rgba(16, 24, 40, 0.08),
      0px 8px 8px -4px rgba(16, 24, 40, 0.03);
    padding: 24px;
    width: 400px;
    // margin-left: auto;
    // margin-right: auto;

    margin: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;
    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 80px;
      .header_pattern {
        position: absolute;
        top: 0;
        left: 0;
      }
      .header_icon .ff-icon {
        position: absolute !important;
        top: 32px;
        width: 48px;
        background: var(--color--primary-lighter);
        height: 48px;
        display: flex;
        justify-content: center;
        border-radius: 50px;
        left: 19px;
        color: var(--color--primary);
      }
      .header_close_btn {
        position: absolute;
        right: 30px;
        top: 44px;
        cursor: pointer;
      }
    }
    .dialog-body {
      padding-bottom: 0;
      text-align: left;
      p {
        color: var(--color--secondary);
      }
    }
    .dialog-footer {
      display: flex;
      align-items: center;
      padding-top: 32px;
      gap: 12px;

      justify-content: flex-end;
      a {
        width: 100%;
      }
    }
  }
}
</style>
