<script setup lang="ts">
interface Props {
  text?: string;
  color?: string;
  textColor?: string;
  margin?: string;
}

const props = withDefaults(defineProps<Props>(), {
  text: "or",
  color: "#e5e7eb",
  textColor: "#6b7280",
  margin: "1.5rem 0",
});
</script>

<template>
  <div class="ff-divider" :style="{ margin: props.margin }">
    <div
      class="ff-divider-line"
      :style="{ backgroundColor: props.color }"
    ></div>
    <span
      class="ff-divider-text"
      :style="{ color: props.textColor }"
      v-if="props.text"
    >
      {{ props.text }}
    </span>
  </div>
</template>

<style scoped lang="scss">
.ff-divider {
  position: relative;
  text-align: center;
}

.ff-divider-line {
  height: 1px;
  width: 100%;
}

.ff-divider-text {
  background-color: white;
  font-size: 0.875rem;
  padding: 0 1rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
