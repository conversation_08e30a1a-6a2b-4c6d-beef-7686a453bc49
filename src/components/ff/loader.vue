<template>
  <div class="loader" :class="`${selectedStyle} ${selectedSize}`"></div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Props {
  size?: string;
  inButton?: boolean;
  inFileIcon?: boolean;
  variant?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: "md", // Your default size value
  inButton: false,
  inFileIcon: false,
  variant: "primary",
});

const styles = {
  primary: "loader--primary",
  white: "loader--white",
  error: "loader--error",
};

const sizes = {
  xs: "size--v-small",
  sm: "size--small",
  md: "size--medium",
  lg: "size--large",
  // ... other sizes
};

const selectedStyle = computed(() => styles[props.variant]);
const selectedSize = computed(() => sizes[props.size]);
</script>

<style lang="scss" scoped>
// @import "assets/scss/variables.scss";

.loader,
.loader:after {
  border-radius: 50%;
}
.loader {
  margin: 0px auto;

  margin: 0 auto;
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 1.1em solid rgb(214 214 214);
  border-right: 1.1em solid rgb(214 214 214);
  border-bottom: 1.1em solid rgb(214 214 214);
  border-left: 1.1em solid var(--color--primary);
  border-top-width: 0.5em;
  border-right-width: 0.5em;
  border-bottom-width: 0.5em;
  border-left-width: 0.5em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 1.1s infinite linear;
  animation: load8 1.1s infinite linear;
  &.loader--primary {
    border-left: 1.1em solid var(--color--primary);
    border-top: 1.1em solid rgba(255, 255, 255, 0.5);
    border-right: 1.1em solid rgba(255, 255, 255, 0.5);
    border-bottom: 1.1em solid rgba(255, 255, 255, 0.5);
  }
  &.loader--white {
    border-left: 1.1em solid #fff;
    border-top: 1.1em solid rgba(255, 255, 255, 0.5);
    border-right: 1.1em solid rgba(255, 255, 255, 0.5);
    border-bottom: 1.1em solid rgba(255, 255, 255, 0.5);
  }
  &.loader--error {
    border-left: 1.1em solid var(--color--error);
    border-top: 1.1em solid rgba(255, 255, 255, 0.9);
    border-right: 1.1em solid rgba(255, 255, 255, 0.9);
    border-bottom: 1.1em solid rgba(255, 255, 255, 0.9);
  }
  &.size--v-small {
    width: 1.5em;
    height: 1.5em;
    border-top-width: 0.3em;
    border-right-width: 0.3em;
    border-bottom-width: 0.3em;
    border-left-width: 0.3em;
    min-height: 19px;
    min-width: 19px;
  }

  &.size--small {
    width: 2em;
    height: 2em;
    border-top-width: 0.3em;
    border-right-width: 0.3em;
    border-bottom-width: 0.3em;
    border-left-width: 0.3em;
  }
  &.size--default {
    width: 3em;
    height: 3em;
  }
  &.size--medium {
    width: 1em;
    height: 1em;
    border-top-width: 0.3em;
    border-right-width: 0.3em;
    border-bottom-width: 0.3em;
    border-left-width: 0.3em !important;
  }
  &.size--large {
     width: 1em;
    height: 1em;
    border-top-width: 0.3em;
    border-right-width: 0.3em;
    border-bottom-width: 0.3em;
    border-left-width: 0.3em !important;
  }
  &.size--v-large {
    width: 5em;
    height: 5em;
  }
  &.inButton {
    width: 15px;
    height: 15px;
    line-height: inherit;
    margin: 0;
    margin-right: 9px;
    @extend .size--v-small;
  }
  &.inFileIcon {
    width: 15px;
    height: 15px;
    line-height: inherit;
    margin: 0;
    margin-right: 8px;

    @extend .size--v-small;
  }
  &.color--white {
    border-top-color: rgba(255, 255, 255, 0.2);
    border-right-color: rgba(255, 255, 255, 0.2);
    border-bottom-color: rgba(255, 255, 255, 0.2);
    border-left-color: var(--color--primary-contrast);
  }
}
@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style>
