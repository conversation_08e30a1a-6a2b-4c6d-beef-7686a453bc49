<template>
  <div class="ElaBreadcrumbs">
    <ol
      class="flex items-center whitespace-nowrap"
      v-if="breadcrumbs.length > 0"
    >
      <li
        class="inline-flex items-center"
        v-for="(item, index) in breadcrumbs"
        :key="index"
      >
        <NuxtLink
          :to="item.route"
          :class="{ 'no-style': noStyle }"
          class="flex items-center text-sm"
        >
          <template v-if="!alwaysShowIcons">
            <template v-if="item.icon && index == breadcrumbs.length - 1">
              <ff-icon :icon="item.icon" size="18" color="inherit"></ff-icon>
            </template>
          </template>
          <template v-else>
            <template v-if="item.icon">
              <ff-icon :icon="item.icon" size="18" color="inherit"></ff-icon>
            </template>
          </template>
          <template v-if="noTranslationFinal">
            <template v-if="index == breadcrumbs.length - 1">
              {{ item.title }}
            </template>
            <template v-else>
              {{ $t("navigation." + item.title) }}
            </template>
          </template>
          <template v-else>
            {{ $t("navigation." + item.title) }}
          </template>
        </NuxtLink>

        <svg
          class="flex-shrink-0 mx-2 overflow-visible size-4 text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          v-if="index < breadcrumbs.length - 1"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="m9 18 6-6-6-6"></path>
        </svg>
      </li>
    </ol>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  breadcrumbs: {
    type: Array,
    required: false,
    default: () => [],
  },
  noStyle: {
    type: Boolean,
    required: false,
    default: false,
  },
  alwaysShowIcons: {
    type: Boolean,
    required: false,
    default: false,
  },
  noTranslationFinal: {
    type: Boolean,
    required: false,
    default: false,
  },
});
</script>

<style lang="scss">
.ElaBreadcrumbs {
  margin: 30px 0;
  ol {
    display: flex;
    align-items: center;
    li {
      display: flex;
      align-items: center;
      &:first-child {
        a {
          padding-left: 0;
        }
      }
      a {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 5px;
        font-weight: 600;
        font-family: var(--font-2);
        color: #222;
        padding: 8px 12px;
        &.no-style {
          padding: 0;
        }
        &.router-link-exact-active {
          background: #f3f3f3;
          border-radius: 6px;

          font-weight: 600;
        }
      }
    }
  }
}
</style>
