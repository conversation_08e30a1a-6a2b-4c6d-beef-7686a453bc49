<!-- components/ff-map.vue -->
<template>
  <input
    v-if="enableAutocomplete"
    ref="autocompleteInput"
    v-model="searchQuery"
    @input="fetchPlaces"
    type="text"
    placeholder="Enter location"
  />
  <div v-if="places.length" class="autocomplete-results">
    <div
      v-for="place in places"
      :key="place.place_id"
      @click="selectPlace(place)"
    >
      {{ place.description }}
    </div>
  </div>
  <div ref="mapElement" class="map"></div>
</template>

<script setup lang="ts">
// import albaniaGeoJson from "~~/assets/json/albania.json";
// import kosovoGeoJson from "~~/assets/json/kosovo.json";
// import macedoniaGeoJson from "@/assets/json/macedonia.json";
import { mapStyling } from "~~/helpers/helper-functions";
import { Loader } from "@googlemaps/js-api-loader";

const props = defineProps<{
  initialPosition: { lat: number; lng: number };
  enableAutocomplete: Boolean;
}>();

const emit = defineEmits(["update:position"]);

const config = useRuntimeConfig();
const mapElement = ref<HTMLDivElement | null>(null);
const searchQuery = ref("");
const places = ref([]);
const autocompleteInput = ref<HTMLInputElement | null>(null);

let map: google.maps.Map;
let marker: google.maps.Marker;
let autocomplete: google.maps.places.Autocomplete;

// New method to fetch places
async function fetchPlaces() {
  if (searchQuery.value.length < 3) {
    // Prevent API calls for very short queries
    places.value = [];
    return;
  }

  const loader = new Loader({
    apiKey: config.public.googleMapsApiKey,
    libraries: ["places"],
  });

  const google = await loader.load();

  const service = new google.maps.places.AutocompleteService();
  service.getPlacePredictions(
    {
      input: searchQuery.value,
      componentRestrictions: { country: ["AL", "XK", "MK"] }, // Keep restrictions
    },
    (predictions, status) => {
      if (
        status !== google.maps.places.PlacesServiceStatus.OK ||
        !predictions
      ) {
        places.value = [];
        return;
      }
      places.value = predictions;
    }
  );
}

onMounted(() => {
  loadGoogleMapsApi().then(() => {
    initMap();
    if (props.enableAutocomplete && autocompleteInput.value) {
      initAutocomplete();
    }
  });
});

function loadGoogleMapsApi() {
  return new Promise<void>((resolve) => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${
      useRuntimeConfig().public.googleMapsApiKey
    }&libraries=places`;
    script.async = true;
    document.head.appendChild(script);
    script.onload = () => resolve();
  });
}
// Method to handle selection of a place
function selectPlace(place) {
  // Logic to handle when a user clicks a place
  // For example, fetching more details and zooming the map
}

function initMap() {
  map = new google.maps.Map(mapElement.value, {
    center: props.initialPosition,
    zoom: 8,
    styles: mapStyling(),
  });

  // Specify your custom marker icon
  const customIcon = {
    url: "/img/marker.svg", // URL or path to your custom marker image
    scaledSize: new google.maps.Size(50, 50), // Optional: Scale the icon to the desired size
    origin: new google.maps.Point(0, 0), // Optional: Origin point of the image
    anchor: new google.maps.Point(25, 49), // Optional: Anchor point, helps in positioning the icon
  };

  marker = new google.maps.Marker({
    position: props.initialPosition,
    map: map,
    icon: customIcon,
    draggable: true,
  });
  marker.addListener("dragend", onMarkerDragEnd);
  // map.data.addGeoJson(albaniaGeoJson);
  // map.data.addGeoJson(kosovoGeoJson);
  // map.data.addGeoJson(macedoniaGeoJson);
  // map.data.setStyle({
  //   fillColor: "transparent",
  //   strokeColor: "000",
  //   strokeWeight: 3,
  // });
}

function initAutocomplete() {
  autocomplete = new google.maps.places.Autocomplete(autocompleteInput.value, {
    types: ["geocode"],
    componentRestrictions: { country: ["AL", "XK", "MK"] },
  });
  autocomplete.bindTo("bounds", map);
  autocomplete.addListener("place_changed", onPlaceChanged);
}

function onPlaceChanged() {
  const place = autocomplete.getPlace();
  console.log(place);
  if (!place.geometry) {
    console.log("Returned place contains no geometry");
    return;
  }

  // If the place has a geometry, then present it on a map.
  if (place.geometry.viewport) {
    map.fitBounds(place.geometry.viewport);
  } else {
    map.setCenter(place.geometry.location);
    map.setZoom(17); // Adjust zoom level as needed for your use case
  }

  // Extracting address components
  const addressComponents = place.address_components;
  const addressObject = {
    country: "",
    streetAddress: "",
    city: "",
    state: "",
    zip: "",
  };

  addressComponents.forEach((component) => {
    const types = component.types;
    if (types.includes("country")) {
      addressObject.country = component.long_name;
    } else if (types.includes("route")) {
      addressObject.streetAddress = `${component.long_name} ${addressObject.streetAddress}`;
    } else if (types.includes("street_number")) {
      addressObject.streetAddress = `${addressObject.streetAddress} ${component.long_name}`;
    } else if (types.includes("locality")) {
      addressObject.city = component.long_name;
    } else if (types.includes("administrative_area_level_1")) {
      addressObject.state = component.long_name;
    } else if (types.includes("postal_code")) {
      addressObject.zip = component.long_name;
    }
  });

  const location = place.geometry.location;
  map.setCenter(location);
  marker.setPosition(location);

  // Emitting the detailed address object along with the new position
  emit("update:position", {
    lat: location.lat(),
    lng: location.lng(),
    address: addressObject,
  });
}
function onMarkerDragEnd() {
  const geocoder = new google.maps.Geocoder();
  const position = marker.getPosition();
  // Set a marker animation on drop
  marker.setAnimation(google.maps.Animation.BOUNCE);

  // Stop the bounce animation after a short delay
  setTimeout(() => {
    marker.setAnimation(null);
  }, 700); // Adjust the duration according to your needs

  geocoder.geocode({ location: position }, (results, status) => {
    if (status === google.maps.GeocoderStatus.OK && results[0]) {
      const addressComponents = results[0].address_components;
      const addressObject = {
        country: "",
        streetAddress: "",
        city: "",
        state: "",
        zip: "",
      };

      addressComponents.forEach((component) => {
        const types = component.types;
        if (types.includes("country")) {
          addressObject.country = component.long_name;
        } else if (types.includes("route")) {
          addressObject.streetAddress += `${component.long_name} `;
        } else if (types.includes("street_number")) {
          addressObject.streetAddress = `${component.long_name} ${addressObject.streetAddress}`;
        } else if (types.includes("locality")) {
          addressObject.city = component.long_name;
        } else if (types.includes("administrative_area_level_1")) {
          addressObject.state = component.long_name;
        } else if (types.includes("postal_code")) {
          addressObject.zip = component.long_name;
        }
      });

      // Ensure streetAddress is trimmed of any leading/trailing spaces
      addressObject.streetAddress = addressObject.streetAddress.trim();

      // Emitting the detailed address object along with the new position
      emit("update:position", {
        lat: position.lat(),
        lng: position.lng(),
        address: addressObject,
      });
    } else {
      console.log("Geocoder failed due to: " + status);
      // Optionally handle the error or fallback scenario
      // For example, you could still emit the position without address details
      emit("update:position", {
        lat: position.lat(),
        lng: position.lng(),
        address: null,
      });
    }
  });
}

defineExpose({ map, marker });
</script>

<style scoped>
.map {
  width: 100%;
  height: 400px;
}

.autocomplete-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  /* Add more styles as needed */
}

.pac-container {
  display: none !important;
}
</style>
