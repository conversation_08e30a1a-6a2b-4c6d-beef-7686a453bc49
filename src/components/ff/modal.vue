<template>
  <transition name="fade">
    <div
      class="ff_modal"
      v-if="show"
      :data-modal-name="modalName"
      :class="[
        { 'has-box-shadow': !backdropVisible, 'no-bg': noBg },
        extraClasses,
      ]"
    >
      <div
        class="modal_backdrop"
        @click="backDropClicked"
        v-if="backdropVisible"
      />
      <div class="modal_dialog" :class="'s-' + size">
        <div class="modal-saving-animation" v-if="modalLoading"></div>
        <template v-if="version == 1">
          <div class="modal_header">
            <div class="header_actions" v-if="!isEmptyObject(actions)">
              <template v-if="actions.close">
                <div class="header_icon close" @click="emitCloseClicked">
                  <ff-icon icon="close"></ff-icon>
                </div>
              </template>
            </div>
            <slot name="header" />
          </div>
        </template>

        <div class="modal_body remove_overflow">
          <slot name="body"></slot>
        </div>

        <template v-if="!hideFooter">
          <div class="modal_footer">
            <slot name="footer"></slot>
          </div>
        </template>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { isEmptyObject } from "~~/helpers/functions";
type ModalActions = {
  close: boolean;
};
const props = defineProps({
  noBg: Boolean,
  modalName: {
    type: String,
    default: "FfStudioModal",
    required: false,
  },
  actions: {
    type: Object as () => ModalActions,
    required: false,
    default: () => {},
  },
  extraClasses: {
    type: String,
    required: false,
  },
  modalLoading: {
    type: Boolean,
    default: false,
    required: false,
  },

  backdropVisible: {
    type: Boolean,
    default: true,
  },
  backDropCloseEnabled: {
    type: Boolean,
    default: true,
    required: false,
  },
  size: {
    type: String,
    default: "default",
    required: false,
  },

  hideFooter: {
    type: Boolean,
    default: false,
    required: false,
  },
  version: {
    type: Number,
    default: 1,
    required: false,
  },
});

const emit = defineEmits(["close"]);
const show = ref(false);

const backDropClicked = () => {
  if (props.backDropCloseEnabled) {
    closeModal();
  }
};

const closeModal = () => {
  show.value = false;
};

const openModal = () => {
  show.value = true;
};

const emitCloseClicked = () => {
  emit("close");
};

// Expose the method
defineExpose({
  openModal,
  closeModal,
});
</script>

<style lang="scss">
// @import '@assets/scss/variables.scss';
@import "@/assets/scss/mixin.scss";
.save_search {
  margin-right: 10px;
}
.modal-saving-animation {
  background-color: #f1f1f1;
  width: 100%;
  height: 6px;
  position: absolute;
  top: 0;
  border-radius: 12px 12px 0 0;
  transition: all 300ms ease-in-out;
  overflow: hidden;
  &:before {
    content: "";
    position: absolute;
    top: 0px;

    left: 0px;
    bottom: 0px;
    background: var(--color--primary);
    animation: box-1 2100ms cubic-bezier(0.65, 0.81, 0.73, 0.4) infinite;
  }

  &:after {
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;

    bottom: 0px;
    background: var(--color--primary);
    animation: box-2 2100ms cubic-bezier(0.16, 0.84, 0.44, 1) infinite;
    animation-delay: 1150ms;
  }
  @keyframes box-1 {
    0% {
      left: -35%;
      right: 100%;
    }
    60%,
    100% {
      left: 100%;
      right: -90%;
    }
  }
  @keyframes box-2 {
    0% {
      left: -200%;
      right: 100%;
    }
    60%,
    100% {
      left: 107%;
      right: -8%;
    }
  }
}

.modal_information {
  position: absolute;
}
.ff_modal {
  overflow-x: hidden;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  z-index: 9999;
  padding: 50px;
  &.less-zindex {
    z-index: 99;
  }
  &.has-box-shadow {
    .modal_dialog {
      box-shadow: 0px 20px 24px -4px rgba(16, 24, 40, 0.08),
        0px 8px 8px -4px rgba(16, 24, 40, 0.03);
    }
  }
  &.no-bg {
    .modal_body {
      &::before {
        content: none;
      }
    }
  }
  .modal_backdrop {
    background-color: rgba(2, 2, 2, 0.2);
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    backdrop-filter: blur(8px);
  }
  .modal_dialog {
    background-color: #ffffff;
    position: relative;

    margin: auto auto;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    z-index: 99;
    width: 571px;
    -webkit-transform: translate3d(0, 0, 0);
    @media screen and (max-width: 992px) {
      width: 90%;
    }
    &.s-mini {
      width: 300px;
      @include respond-below(sm) {
        width: 95%;
      }
    }
    &.s-smaller {
      width: min(90%, 500px);
      @include respond-below(sm) {
        width: 95%;
      }
    }
    &.s-default {
      width: 600px;
      @include respond-below(sm) {
        width: 80vw;
      }
    }
    &.s-medium {
      width: 850px;
      @include respond-below(sm) {
        width: 95%;
      }
    }
    &.s-large {
      width: 1200px;
      @include respond-below(sm) {
        width: 95%;
      }
    }
  }
  .modal_close {
    width: 30px;
    height: 30px;
  }
  .modal_header_image {
    margin: 10px 0;
  }

  .modal_header {
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f1f1f1;
    position: relative;
    p {
      font-size: 18px;
      color: #101828;
    }
    .header_actions {
      .header_icon {
        &.close {
          position: absolute;
          left: 10px;
          top: 20px;
        }
        svg {
          color: #98a2b3 !important;

          &:hover {
            transform: initial;
            cursor: initial;
          }
        }
      }
    }
  }
  .modal_body {
    position: relative;
    padding: 40px 45px;
    min-height: 100px;
    height: auto;
    // overflow: auto;

    display: flex;
    // min-height: 250px;
    max-height: auto;
    overflow-y: auto;
    flex-direction: column;
    align-items: stretch;
    &:before {
      content: "";
      position: absolute;
      height: 170px;
      width: 100%;
      left: 0;
      top: 0;
      z-index: -1;
      background: linear-gradient(to bottom, #2474473d, #fff);
      overflow: hidden;
      border-radius: 12px;
    }
    &.remove_overflow {
      overflow-y: visible;
    }

    .FfStudioModal-new-table .table thead th,
    .FfStudioModal-new-table tr td {
      padding: 0 15px !important;

      @include respond-below(sm) {
        padding: 0 15px !important;
      }
    }

    .FfStudioModal-new-table .table {
      margin-bottom: 60px;
    }
    .FfStudioModal-new-table .table tr:hover {
      background: transparent;
    }

    h3 {
      font-size: clamp(24px, 2vw, 30px);
    }

    .modal_back_to {
      display: flex;
      align-items: center;
      margin: 5px 0;
      .left {
      }
      .right {
        margin-left: 10px;
        display: flex;
        p {
          position: relative;
          &:after {
            position: relative;
            content: ",";
          }
          &:last-child {
            &:after {
              display: none;
            }
          }
        }
      }
    }
    &.no-padding {
      padding: initial;
    }

    &.version--2 {
      text-align: center;
      align-items: center;
      display: block;
      margin: 2vw;
      &.no-margin {
        .icon_with_title {
          margin-bottom: 0;
        }
      }
      .icon_with_title {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
        align-items: center;
        margin-bottom: 30px;
        i {
          font-size: 25px;
          background: var(--color--primary-lighter);
          color: var(--color--primary);
          width: 50px;
          height: 50px;
          border-radius: 53px;
          line-height: 48px;
          margin-bottom: 10px;
          &.no-styling {
            font-size: initial;
            background: initial;
            color: initial;
            width: initial;
            height: initial;
            border-radius: initial;
            line-height: initial;
            margin-bottom: initial;
          }
        }
        h5 {
          margin: 0;
        }
        p {
          margin-bottom: 20px;
        }
      }
    }
  }
  .modal_footer_extra {
    display: flex;
    justify-content: space-between;
    padding: 10px 30px;
    align-items: center;
    border-top: 1px solid #f1f1f1;
    a {
      font-weight: 500 !important;
    }
  }
  .modal_footer {
    background: none;
    display: flex;
    align-items: center;
    position: relative;
    bottom: 0;
    width: 100%;
    gap: 10px;
    justify-content: center;
    padding-top: 0;
    padding-bottom: 30px;
    padding-left: 45px;
    padding-right: 45px;
    &.b-placement-right {
      justify-content: flex-end;
    }
    &.b-placement-center {
      justify-content: center;
    }
    &.b-placement-left {
      justify-content: flex-start;
    }
    &.b-placement-space-between {
      justify-content: space-between;
    }
    .FfStudioModal-button-container {
      button {
        margin-right: 10px;
      }
      &:last-child {
        button {
          margin-right: 0;
        }
      }
    }
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.modal_body_inner {
  .page-actions {
    margin: 0 !important;
  }
}
</style>
