<template>
  <div
    class="ff_nothing_found"
    :class="{
      reset_styles: type == 'vehicle-images' || type == 'vehicle-feature-image',
      half_with: type == 'vehicle-pricing',
    }"
  >
    <template v-if="type == 'vehicle-pricing'">
      <div class="ff_nothing_title relative">
        <p class="nothing_title mb-2">Flexible Rate Scheduling</p>
        <p class="mt-2">
          Unlock unparalleled flexibility with Flexible Rate Scheduling! Tailor
          your rental rates to match demand and special occasions effortlessly.
          Set specific prices for different dates, ensuring optimal revenue and
          customer satisfaction. Experience the future of rental pricing with
          precision and ease.
        </p>
        <ff-illustration type="pricing-schedule" />
        <ff-button
          type="primary"
          @click="emitToButtonClickRequested"
          class="mt-2 w-full"
          ><ff-icon icon="plus" color="inherit"></ff-icon> Set Special
          Rates</ff-button
        >
      </div>
    </template>
    <template v-if="type == 'vehicle-feature-image'">
      <ff-illustration type="files" icon="star" />
      <div class="ff_nothing_title">
        <p class="nothing_title">No featured image added</p>
        <p class="text-gray-600 mt-2">
          Please choose a featured image for your vehicle.
        </p>
      </div>
    </template>
    <template v-if="type == 'vehicle-images'">
      <ff-illustration type="files" icon="image" />
      <div class="ff_nothing_title">
        <p class="nothing_title">No vehicles images added</p>
        <p class="text-gray-600 mt-2">
          Currently your post doesn’t have any vehicle images added. Please
          upload some.
        </p>
      </div>
    </template>
    <template v-if="type == 'vehicles'">
      <ff-illustration type="cloud" icon="cars" />
      <div class="ff_nothing_title">
        <p class="nothing_title">No vehicles added</p>
        <p class="text-gray-600 mt-2">
          Currently you don’t have any new vehicles added at the moment. Please
          go ahead and add new vehicles by clicking button below.
        </p>
      </div>
      <ff-button type="primary" @click="emitToButtonClickRequested"
        ><ff-icon icon="plus" color="inherit"></ff-icon> Add new
        vehicle</ff-button
      >
    </template>
    <template v-else-if="type == 'team_member'">
      <ff-illustration type="cloud" icon="user" />
      <div class="ff_nothing_title">
        <p class="nothing_title">No team member added</p>
        <p class="text-gray-600 mt-1">
          Currently you don’t have any team members added at the moment.Please
          go ahead and add new member by clicking button below.
        </p>
      </div>
      <ff-button type="primary" @click="emitToButtonClickRequested"
        ><ff-icon icon="plus" color="inherit"></ff-icon> Add new
        member</ff-button
      >
    </template>
    <template v-else-if="type == 'company_locations'">
      <ff-illustration type="cloud" icon="location" />
      <div class="ff_nothing_title">
        <p class="nothing_title">No locations added</p>
        <p class="text-gray-600 mt-1">
          Currently you don't have any pick-up or drop-off locations set for
          your vehicles.
        </p>
      </div>
      <ff-button type="primary" @click="emitToButtonClickRequested"
        ><ff-icon icon="plus" color="inherit"></ff-icon> Add new
        location</ff-button
      >
    </template>
    <template v-else-if="type == 'no_feature'">
      <ff-illustration type="cloud" icon="location" />
      <div class="ff_nothing_title">
        <p class="nothing_title">No feature added</p>
        <p class="text-gray-600 mt-1">
          Currently you don't have any features added at the moment. Please go
          ahead and add new feature by clicking button below.
        </p>
      </div>
      <ff-button type="primary" @click="emitToButtonClickRequested"
        ><ff-icon icon="plus" color="inherit"></ff-icon> Add new
        location</ff-button
      >
    </template>
    <template v-else-if="type == 'team_invitations'">
      <ff-illustration type="team_member" icon="email" />
      <div class="ff_nothing_title">
        <p class="nothing_title">No invitations found</p>
        <p class="text-gray-600 mt-1">Currently you don't have any invites</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { eventBus } from "~~/helpers/helper-functions";

interface Props {
  type: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: "",
});

const emits = defineEmits(["AddNewClicked"]);
// This event will fire and check if there is a listener to this
const emitToButtonClickRequested = () => {
  emits("AddNewClicked");
};
</script>

<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";
.ff_nothing_found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 40%;
  margin: 60px auto;
  text-align: center;
  gap: 20px;
  &.half_with {
    max-width: 80%;
    margin: 0 auto;
    padding: 80px;
    @include respond-below(md) {
      padding: 40px;
      max-width: 100%;
    }
  }
  &.reset_styles {
    max-width: 100%;
    margin: 0;
  }
  .ff_nothing_title {
    p {
      &.nothing_title {
        color: #101828;
        text-align: center;
        font-size: 22px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
      }
      &.nothing_description {
        color: #222222;
        text-align: center;
        margin-top: 15px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
}
</style>
