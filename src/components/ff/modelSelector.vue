<template>
  <template v-if="loginError">
    {{ loginError }}
  </template>

  <ElaDropdown
    :pt="componentClasses"
    :model-value="value"
    @change="updateModelValue"
    :loading="optionsLoading"
    :options="options"
    :disabled="!make"
    optionLabel="name"
    :placeholder="
      optionsLoading
        ? $t('general.loading')
        : !make
        ? $t('inputs.pleaseSelectMakeFirst')
        : $t('inputs.select_model')
    "
  >
    <template #content="slotProps">
      {{ slotProps }}
    </template>
    <template #value="slotProps">
      <div v-if="slotProps.value" class="ElaDropdownRow is_selected">
        <div>{{ make.name }} {{ slotProps.value.name }}</div>
      </div>
      <span v-else>
        {{ slotProps.placeholder }}
      </span>
    </template>
    <template #option="slotProps">
      <div class="ElaDropdownRow">
        <div>{{ make.name }} {{ slotProps.option.name }}</div>
      </div>
    </template>
  </ElaDropdown>

  <div class="input_errors" v-if="errorMessage">
    <p class="error">{{ $t("validations." + errorMessage) }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
// Prime Vue classes overrides
import { ElaDropdownClasses } from "@/utilities/PrimeVueClasses";
const componentClasses = ref(ElaDropdownClasses);

const optionsLoading = ref(false);
const selectedCountry = ref();
const options = ref([]);
const { get_vehicle_make_models } = useVehicles();
const loginError = ref<string | null>(null);

const props = defineProps({
  value: Object,
  make: Object,
  errorMessage: String,
});
const emits = defineEmits(["update:value", "listCarSelected"]);

function updateModelValue(newValue: any) {
  emits("update:value", newValue.value);
}

const load_options = async () => {
  console.log(props.make);
  if (!props.make) {
    loginError.value = "please_select_make";
    return;
  }

  optionsLoading.value = true; // Start loading
  try {
    let response = await get_vehicle_make_models(props.make.id);
    if (response.data.success) {
      options.value = response.data.data.models;
    }
  } catch (err) {
    // notify(handleError(err), "error", 5000);
  } finally {
    optionsLoading.value = false; // End loading
  }
};

defineExpose({ load_options });

const select_make_from_list = async (e) => {
  emits("listCarSelected", e);
};
</script>
