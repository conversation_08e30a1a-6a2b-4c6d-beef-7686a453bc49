<template>
  <div class="ff_country_flag" :class="`${extraClass}`">
    <template v-if="country == 'XK'">
      <img
        src="https://upload.wikimedia.org/wikipedia/commons/1/1f/Flag_of_Kosovo.svg"
        alt="Kosovo"
      />
    </template>
    <template v-else-if="country == 'AL'">
      <img
        src="https://upload.wikimedia.org/wikipedia/commons/3/36/Flag_of_Albania.svg"
        alt="Albania"
      />
    </template>
    <template v-else-if="country == 'MK'">
      <img
        src="https://upload.wikimedia.org/wikipedia/commons/7/79/Flag_of_North_Macedonia.svg"
        alt="North Macedonia"
      />
    </template>
    <template v-else-if="country == 'EU'">
      <img
        src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b7/Flag_of_Europe.svg/2560px-Flag_of_Europe.svg.png"
        alt="Europe"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  country: {
    type: [String, Number],
    required: false,
    default: "",
  },
  size: {
    type: String,
    required: false,
    default: "sm",
  },
});

const extraClass = computed(() => {
  return props.size ? `flag_${props.size}` : "";
});

const emit = defineEmits(["@changed"]);
</script>

<style lang="scss" scoped>
.ff_country_flag {
  display: inline-flex;

  &.flag_sm {
    img {
      width: 24px;
      height: 24px;
      object-fit: cover;
      border-radius: 50px;
      min-width: 24px;
    }
  }
  &.flag_xs {
    img {
      width: 15px;
      height: 15px;
      object-fit: cover;
      border-radius: 50px;
      min-width: 15px;
    }
  }
}
</style>
