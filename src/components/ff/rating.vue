<template>
  <div class="ff_rating">
    <template v-if="rating > 0">
      {{ rating }} <ff-icon icon="star" size="18" color="red"></ff-icon>
      <span v-if="reviewers && reviewers > 0"> ( {{ reviewers }} ) </span>
    </template>
  </div>
</template>

<script setup lang="ts">
interface Props {
  rating: number;
  reviewers?: number;
}

const props = withDefaults(defineProps<Props>(), {
  rating: 0,
  reviewers: undefined,
});
</script>

<style lang="scss" scoped>
.ff_rating {
  display: flex;
  align-items: center;
  font-size: 17px;
  font-weight: 500;
  gap: 4px;
}
</style>
