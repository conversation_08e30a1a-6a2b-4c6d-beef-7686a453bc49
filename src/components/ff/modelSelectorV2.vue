<template>
  <div class="listing-list-container">
    <ff-input
      v-if="!optionsLoading"
      :label="$t('inputs.search_vehicle_model')"
      v-model="searchQuery"
      ref="RefInputVehicleEngine"
      id="email"
      placeholder="530, 520 , etc."
      :disabled="false"
      :extra-class="'mb-2'"
    />

    <template v-if="optionsLoading">
      <ff-loader />
    </template>
    <template v-else-if="filteredModels.length > 0">
      <div class="list">
        <div
          class="listing-list-item"
          v-for="(model, index) in filteredModels"
          :key="index"
          @click="select(model)"
          :class="{ active: value && value.id == model.id }"
        >
          <ff-icon
            icon="checkmark"
            v-if="value && value.id == model.id"
            class="checked_icon"
            color="inherit"
          ></ff-icon>

          <p>{{ model.name }}</p>
        </div>
      </div>
    </template>
  </div>

  <div class="input_errors" v-if="errorMessage">
    <p class="error">{{ $t("validations." + errorMessage) }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElaDropdownClasses } from "@/utilities/PrimeVueClasses";
import { useAddVehicleStore } from "@/stores/user/vehicles/store-add-vehicle";

const AddVehicleStore = useAddVehicleStore();

const componentClasses = ref({ ...ElaDropdownClasses });

const type = ref("list");
const optionsLoading = ref(true);
const options = ref([]);
const important_makes = ref([]);
const searchQuery = ref(""); // Add search query ref
const { get_vehicle_make_models } = useVehicles();

const props = defineProps({
  value: Object,
  errorMessage: String,
});
const emits = defineEmits(["update:value", "change"]);
const VehicleData = computed(() => AddVehicleStore.getSingleVehicleData);

onMounted(() => {
  load_options();
});

const filteredModels = computed(() => {
  return options.value.filter((model) =>
    model.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

function updateModelValue(newValue: any) {
  emits("update:value", newValue.value);
}

const load_options = async () => {
  if (
    typeof VehicleData.value.make !== "undefined" &&
    VehicleData.value.make !== null
  ) {
    optionsLoading.value = true; // Start loading

    try {
      let response = await get_vehicle_make_models(VehicleData.value.make.id);
      if (response.data.success) {
        options.value = response.data.data.models;
      }
    } catch (err) {
      // notify(handleError(err), "error", 5000);
    } finally {
      optionsLoading.value = false; // End loading
    }
  }
};

// Computed properties to filter the car makes based on the search query
const filteredImportantMakes = computed(() => {
  return important_makes.value.filter((make) =>
    make.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const filteredOptions = computed(() => {
  return options.value.filter((make) =>
    make.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const select = async (model) => {
  emits("change", { id: model.id, model_object: model });
};
</script>

<style lang="scss"></style>
