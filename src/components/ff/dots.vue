<template>
  <div class="ff-dots">
    <div class="dots-container">
      <div class="dot dot-1" :style="dotStyle"></div>
      <div class="dot dot-2" :style="dotStyle"></div>
      <div class="dot dot-3" :style="dotStyle"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

type SizeType = "xs" | "sm" | "md" | "lg" | "xl";
type SpeedType = "slow" | "normal" | "fast";

interface Props {
  size?: SizeType;
  color?: string;
  speed?: SpeedType;
}

const props = withDefaults(defineProps<Props>(), {
  size: "md",
  color: "",
  speed: "normal",
});

const sizes: Record<SizeType, string> = {
  xs: "8px",
  sm: "10px",
  md: "12px",
  lg: "16px",
  xl: "20px",
};

const speeds: Record<SpeedType, string> = {
  slow: "2s",
  normal: "1.4s",
  fast: "1s",
};

const dotStyle = computed(() => ({
  width: sizes[props.size],
  height: sizes[props.size],
  backgroundColor: props.color || "var(--color--primary, #3b82f6)",
  animationDuration: speeds[props.speed],
}));
</script>

<style scoped lang="scss">
.ff-dots {
  display: inline-flex;
  align-items: center;
  justify-content: center;

  .dots-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .dot {
    border-radius: 50%;
    animation: dotPulse var(--animation-duration, 1.4s) infinite ease-in-out;
  }

  .dot-1 {
    animation-delay: 0s;
  }

  .dot-2 {
    animation-delay: 0.2s;
  }

  .dot-3 {
    animation-delay: 0.4s;
  }
}

@keyframes dotPulse {
  0%,
  80%,
  100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
