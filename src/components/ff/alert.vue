<template>
  <div class="container">
    <div
      class="alert"
      :class="[typeClass, extraClass, isActive]"
      :aria-live="ariaLive"
    >
      <div class="alert-content">
        <div class="icon-container">
          <ff-Icon icon="warning" size="1.5x" />
        </div>
        <div class="text-container">
          <span class="alert-message">{{ alertMessage }}</span>
          <span class="text-message">{{ textMessage }}</span>
        </div>
        <div class="close-container" @click="closeAlert">
          <ff-Icon icon="close" size="1.5x" />
        </div>
      </div>
    </div>
  </div>
  <div class="border"></div>
</template>

<script setup lang="ts">
type TooltipConfigType = {
  left?: { text: string; position: string };
  right?: { text: string; position: string };
};

import type { PropType } from "vue";

const props = defineProps({
  type: String,
  extraClass: String,
});

const emit = defineEmits(["close"]);

const closeAlert = () => {
  emit("close");
};

const alertMessage = computed(() => {
  switch (props.type) {
    case "warning":
      return "Warning:";
    case "success":
      return "Success:";
    case "error":
      return "Error:";
    case "info":
      return "Info:";
    default:
      return "Info:";
  }
});

const textMessage = computed(() => {
  switch (props.type) {
    case "warning":
      return "Please be cautious!";
    case "success":
      return "Operation successful!";
    case "error":
      return "Critical error occurred.";
    case "info":
      return "Important information or updates.";
    default:
      return "Default text message.";
  }
});

const typeClass = computed(() => `alert-${props.type || "info"}`);
const isActive = computed(() => (props.active ? "is_active" : null));
const ariaLive = computed(() =>
  props.type === "error" ? "assertive" : "polite"
);

const iconMapping = {
  warning: "warning-icon",
  success: "success-icon",
  error: "error-icon",
  info: "info-icon",
};

// const computedIcon = computed(() => iconMapping[props.type] || 'default-icon');
</script>

<style scoped>
.container {
  border-top: 1px solid #eaecf0;
  border-bottom: 1px solid #eaecf0;
  margin: 0;
}

.alert {
  display: flex;
  align-items: center;
}

.alert-content {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 12px 0 12px 0;
}

.icon-container {
  margin-right: 10px;
  display: flex;
  height: 20px !important;
}

.text-container {
  margin-right: 10px;
  display: flex;
}

.close-container {
  margin-left: auto;
  cursor: pointer;
}

.alert-message {
  font-family: var(--font-2);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  margin-right: 5px;
}

.text-message {
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

/* Add more styles if needed */

.warning-icon {
  /* Add styles or path for your warning icon */
}

.success-icon {
  /* Add styles or path for your success icon */
}

.error-icon {
  /* Add styles or path for your error icon */
}

.info-icon {
  /* Add styles or path for your info icon */
}

.default-icon {
  /* Add styles or path for a default icon or use a default one */
}
</style>
