<template>
  <div class="ff-select-box">
    <div
      class="ff-select-box-item"
      v-for="option in options"
      :key="option.id"
      @click="toggleSelection(option)"
      :class="{ selected: isOptionSelected(option) }"
    >
      <div class="option-icon" v-if="option.icon">
        <ff-icon :icon="option.icon" fill="none" width="30px" height="30px" />
      </div>
      <h6>{{ option.label }}</h6>
      <p class="smaller" v-if="option.description">
        {{ option.description }}
      </p>
    </div>
  </div>

  <div class="ff_input" v-if="errorMessage">
    <div class="input_errors">
      <p class="error">{{ $t("validations." + errorMessage) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { InputBoxSelectionOption } from "#types";
// import { email } from '@/assets/icons/email'
const props = defineProps({
  options: Array as () => InputBoxSelectionOption[],
  value: Array as () => InputBoxSelectionOption[],
  multiple: Boolean,
  errorMessage: String,
});

const selectedOptions = ref(props.value || []);

const emit = defineEmits(["update:modelValue"]);

// Emit event with updated value
const emitInputEvent = () => {
  emit(
    "update:modelValue",
    props.multiple ? selectedOptions.value : selectedOptions.value[0]
  );
};

const toggleSelection = (option: InputBoxSelectionOption) => {
  if (props.multiple) {
    // Multiple selection allowed
    const index = selectedOptions.value.findIndex(
      (selectedOption) => selectedOption.id === option.id
    );

    if (index === -1) {
      // Option not selected, add it to the selectedOptions array
      selectedOptions.value.push(option);
    } else {
      // Option already selected, remove it from the selectedOptions array
      selectedOptions.value.splice(index, 1);
    }
  } else {
    // Single selection
    selectedOptions.value = [option];
  }

  // Emit an 'input' event with the updated selected options
  emitInputEvent();
};

const isOptionSelected = (option: InputBoxSelectionOption) => {
  return selectedOptions.value.some(
    (selectedOption) => selectedOption.id === option.id
  );
};
</script>

<style scoped lang="scss">
// @import '@assets/scss/variables.scss';
.ff-select-box {
  display: flex;
  gap: 5px;
  &-item {
    display: flex;
    height: 189px;
    padding: 20px;

    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: var(--radius-md, 8px);

    border: 2px solid #d0d5dd;

    background: #fff;
    gap: 8px;
    text-align: center;
    cursor: pointer;
    &.selected {
      border-color: #000;
    }
  }

  .input_errors {
    color: red;
  }
}
</style>
