<template>
  <div
    :class="[
      'ff_input',
      extraClass,
      { 'has-error': hasError, disabled: disabled },
    ]"
  >
    <template v-if="hasRightLabel">
      <div class="ff_label_with_action">
        <label :for="id" v-if="label">{{ label }}</label>

        <template v-if="rightLabelDirection">
          <nuxt-link :to="rightLabelDirection">
            {{ rightLabelText }}
          </nuxt-link>
        </template>
        <template v-else-if="rightLabelClicked">
          <a @click.prevent="rightLabelClicked">
            {{ rightLabelText }}
          </a>
        </template>
      </div>
    </template>
    <template v-else>
      <label :for="id" v-if="label">{{ label }}</label>
    </template>

    <div
      class="ff-input-container"
      :class="{
        'input-with-left-icon': leftIcon,
        'input-with-left-leading-text': leftLeadingText,
        'input-with-right-leading-text': rightLeadingText,
        'input-with-right-icon': rightIcon,
        'ff-input-with-icon': leftIcon || rightIcon,
      }"
    >
      <span
        v-if="leftIcon"
        class="icon_item left-icon"
        @click="onIconClick('left')"
      >
        <component
          v-if="iconMap[leftIcon]"
          :is="iconMap[leftIcon]"
          v-bind="defaultIconProps"
        />
      </span>
      <span v-if="leftLeadingText" class="left-leading-text">
        {{ leftLeadingText }}
      </span>

      <!-- Inline suggestion overlay -->

      <template v-if="mask">
        <input
          :autocomplete="autocomplete"
          v-mask="mask"
          ref="inputElement"
          :id="id"
          :placeholder="placeholder"
          v-model="internalValue"
          @input="updateMaskValue"
          @blur="emitBlur"
          @keydown.tab="onTabPressed"
          @keydown.enter="onEnterPressed"
          :disabled="disabled"
        />
      </template>
      <template v-else-if="isMoney">
        <input
          :autocomplete="autocomplete"
          v-money3="moneyConfig"
          ref="inputElement"
          :id="id"
          :placeholder="placeholder"
          v-model.lazy="moneyValue"
          @input="updateMoneyValue"
          @blur="emitBlur"
          @keydown.tab="onTabPressed"
          @keydown.enter="onEnterPressed"
          :disabled="disabled"
        />
      </template>
      <template v-else>
        <template v-if="type == 'textarea'">
          <textarea
            :autocomplete="autocomplete"
            :type="type"
            v-bind="maxNumber !== undefined ? { maxlength: maxNumber } : {}"
            ref="inputElement"
            :id="id"
            :placeholder="placeholder"
            :value="internalValue"
            @input="updateValue"
            @blur="emitBlur"
            rows="50"
            @keydown.tab="onTabPressed"
            @keydown.enter="onEnterPressed"
            :disabled="disabled"
          />
        </template>
        <template v-else>
          <input
            :autocomplete="autocomplete"
            :type="inputType || type"
            v-bind="
              maxNumber !== undefined
                ? { max: maxNumber, 'v-limit-length': maxNumber }
                : {}
            "
            ref="inputElement"
            :id="id"
            :placeholder="placeholder"
            :value="internalValue"
            @input="updateValue"
            @focus="onFocus"
            @blur="emitBlur"
            @keydown="onKeyDown"
            @keydown.tab="onTabPressed"
            @keydown.enter="onEnterPressed"
            :disabled="disabled"
          />
        </template>
      </template>
      <span
        v-if="rightIcon"
        class="icon_item right-icon"
        @click="onIconClick('right')"
        :class="{ cursor: rightIconClickable }"
      >
        <component
          v-if="iconMap[rightIcon]"
          :is="iconMap[rightIcon]"
          v-bind="defaultIconProps"
        />
      </span>
      <span v-if="rightLeadingText" class="right-leading-text">
        {{ rightLeadingText }}
      </span>
    </div>

    <!-- Suggestions Dropdown -->

    <div class="input_errors" v-if="displayError">
      <p class="error">{{ displayError }}</p>
    </div>
    <template v-else-if="hint">
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>

    <template v-else-if="countChars && internalValue !== null">
      <div class="input_errors">
        <p class="hint">{{ internalValue?.length }} / {{ maxCharsNumber }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
import { ref, watch, computed, inject } from "vue";
import { iconMap, type IconName } from "./iconMap";

import { useField } from "vee-validate";
import { z } from "zod";

// import { email } from '@/assets/icons/email'
const inputElement = ref<HTMLElement | null>(null);
const props = defineProps({
  modelValue: String,
  value: String,
  hasRightLabel: Boolean,
  rightLabelText: String,
  rightLabelDirection: String,
  leftIcon: {
    type: String as PropType<IconName>,
    required: false,
  },
  leftIconTooltip: String,
  leftLeadingText: String,
  rightLeadingText: String,
  max: {
    type: [String, Number],
    required: false,
  },
  rightIcon: {
    type: String as PropType<IconName>,
    required: false,
  },
  mask: {
    type: String,
    required: false,
    default: "",
  },
  isMoney: {
    type: Boolean,
    default: false,
  },
  rightIconTooltip: String,
  rightIconClickable: {
    type: Boolean,
    default: false,
  },
  label: String,
  type: {
    type: String,
    default: "text",
  },
  hint: String,
  id: String,
  placeholder: String,
  disabled: Boolean,
  autocomplete: String,
  extraClass: String,
  errorMessage: String,
  countChars: Boolean,
  maxChars: {
    type: [String, Number],
    required: false,
  },
  // Validation props
  validationType: {
    type: String as PropType<
      "email" | "password" | "phone" | "required" | "none"
    >,
    default: "none",
  },
  validationMessage: String,

  // Input type for different data types
  inputType: {
    type: String as PropType<
      "text" | "tel" | "email" | "number" | "password" | "url"
    >,
    default: "text",
  },
  // Form integration props
  name: {
    type: String,
    required: false,
  },
});

function focus() {
  inputElement.value?.focus();
}

function clearValidationError() {
  internalError.value = "";
}

const moneyConfig = {
  masked: false,
  precision: 2,
  thousands: ",",
  decimal: ".",
  prefix: "",
  suffix: "",
};

// Inject form context if available
const formContext = inject("formContext", null);

// Check if we should use vee-validate (when inside BForm and name is provided)
const shouldUseVeeValidate = formContext && props.name;

// Setup vee-validate field if inside a form
const veeField = shouldUseVeeValidate
  ? useField(props.name, undefined, {
      syncVModel: true,
      initialValue: props.modelValue || "",
    })
  : null;

// Use vee-validate value and error if available, otherwise use internal state
const internalValueRef = shouldUseVeeValidate ? null : ref(props.modelValue);

const internalError = ref<string>("");

// Computed property for the current value
const internalValue = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return veeField.value.value;
  }
  return internalValueRef?.value;
});

// Validation schemas
const emailValidator = z.string().email("Please enter a valid email address");
const passwordValidator = z
  .string()
  .min(8, "Password must be at least 8 characters");
const phoneValidator = z.string().min(10, "Please enter a valid phone number");
const requiredStringValidator = (fieldName: string) =>
  z.string().min(1, `${fieldName} is required`);

// Get validation functions

const moneyValue = ref(props.modelValue || props.value || "");

const onFocus = () => {};

const onSuggestionClick = (
  suggestion: string | { id: string; label: string }
) => {
  const value = typeof suggestion === "string" ? suggestion : suggestion.label;
  if (shouldUseVeeValidate && veeField) {
    veeField.value.value = value;
  } else {
    if (internalValueRef) {
      internalValueRef.value = value;
    }
  }
  emit("update:modelValue", value);
  emit("change", value);
};

// Handle Tab key to accept inline suggestion
const onKeyDown = (event: KeyboardEvent) => {
  // if (
  //   event.key === "Tab" &&
  //   inlineSuggestion.value &&
  //   props.showInlineSuggestion
  // ) {
  //   event.preventDefault();
  //   emit("update:modelValue", newValue);
  //   emit("change", newValue);
  // }
};

const hasError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const displayError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const emit = defineEmits([
  "update:modelValue",
  "iconClick",
  "change",
  "blur",
  "tabPressed",
  "enterPressed",
  "rightLabelClicked",
]);

const updateValue = (event: Event) => {
  const newValue = (event.target as HTMLInputElement).value;

  if (shouldUseVeeValidate && veeField) {
    // If using vee-validate, update the field value
    veeField.value.value = newValue;
  } else {
    // Traditional approach
    if (internalValueRef) {
      internalValueRef.value = newValue;
    }

    // Clear error when user starts typing (if there's an error)
    if (internalError.value && newValue.length > 0) {
      internalError.value = "";
    }
  }

  if (props.isMoney) {
    moneyValue.value = newValue;
  }
  emit("update:modelValue", newValue);
  emit("change", newValue);
};

const rightLabelClicked = () => {
  emit("rightLabelClicked");
};

const updateMaskValue = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = target.value;

  if (shouldUseVeeValidate && veeField) {
    veeField.value.value = newValue;
  } else {
    if (internalValueRef) {
      internalValueRef.value = newValue;
    }

    // Clear error when user starts typing (if there's an error)
    if (internalError.value && newValue.length > 0) {
      internalError.value = "";
    }
  }

  emit("update:modelValue", newValue);
  emit("change", newValue);
};

const updateMoneyValue = (event: Event) => {
  const newValue = (event.target as HTMLInputElement).value;
  moneyValue.value = newValue;

  // Convert to number for money inputs, removing commas and parsing as float
  const numericValue = newValue ? parseFloat(newValue.replace(/,/g, "")) : 0;

  if (shouldUseVeeValidate && veeField) {
    // For vee-validate, we might still need to store as string depending on schema
    veeField.value.value = newValue;
  } else {
    // Clear error when user starts typing (if there's an error)
    if (internalError.value && newValue.length > 0) {
      internalError.value = "";
    }
  }

  // Emit the numeric value for money inputs
  emit("update:modelValue", numericValue);
  emit("change", numericValue);
};
const onIconClick = (position: "left" | "right") => {
  emit("iconClick", position);
};
const emitBlur = () => {
  emit("blur");
};
const onTabPressed = (event: any) => {
  emit("tabPressed", event);
};
const onEnterPressed = (event: any) => {
  emit("enterPressed", event);
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (shouldUseVeeValidate && veeField) {
      veeField.value.value = newValue || "";
    } else {
      if (internalValueRef) {
        internalValueRef.value = newValue;
      }
    }

    // Update moneyValue for money inputs to sync display
    if (props.isMoney) {
      moneyValue.value = newValue || "";
    }
  }
);

const maxNumber = computed(() => {
  const n = Number(props.max);
  return isNaN(n) ? undefined : n;
});
const maxCharsNumber = computed(() => {
  const n = Number(props.maxChars);
  return isNaN(n) ? undefined : n;
});

const defaultIconProps = {
  size: 20,
};

defineExpose({ focus, clearValidationError });
</script>

<style scoped lang="scss">
.ff_input {
  position: relative;
}

.ff-input-container {
  position: relative;

  &.has-inline-suggestion {
    input {
      position: relative;
      z-index: 2;
      background: transparent !important;
      color: rgba(0, 0, 0, 1);
    }
  }
}

.inline-suggestion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  z-index: 1;
  font-family: inherit;
  font-size: inherit;

  .typed-text {
    color: transparent;
    white-space: pre;
    font-family: inherit;
    font-size: inherit;
  }

  .suggestion-text {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
