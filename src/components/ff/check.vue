<template>
  <div :class="['ff_checkbox', extraClass]">
    <div
      class="ff-input-container"
      @click="updateValue"
      :class="{ checked: isChecked, disabled: disabled }"
    >
      <div
        class="checkmark"
        :style="{ backgroundColor: isChecked ? '#EF405C' : '#D0D5DD' }"
      >
        <svg
          v-if="isChecked"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10Z"
            fill="#EF405C"
          />
          <path
            d="M6.25 10L8.75 12.5L13.75 7.5"
            stroke="white"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <span class="text">{{ label }}</span>
    </div>
    <div class="input_errors" v-if="errorMessage">
      <p class="error">{{ errorMessage }}</p>
    </div>
    <template v-else>
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { array } from "yup";

const props = defineProps({
  modelValue: Boolean,
  label: String,
  id: String,
  extraClass: Array,
  disabled: Boolean,
  errorMessage: String,
  hint: String,
});

const isChecked = ref(props.modelValue);
const emit = defineEmits(["update:modelValue"]);

const updateValue = () => {
  if (!props.disabled) {
    isChecked.value = !isChecked.value;
    emit("update:modelValue", isChecked.value);
  }
};

// Watch for changes in props.modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    isChecked.value = newValue;
  }
);
</script>

<style scoped>
.ff_checkbox {
  /* Add your styles for the checkbox container */
}

.ff-input-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkmark {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  border: none !important;
  border-radius: 15%;
}

.checkmark span {
  font-size: 14px;
}

.checked .checkmark {
  border: 1px solid #ef405c; /* Purple color for the border when checked */
}

.text {
  font-size: 14px;
  font-weight: 500;
  font-family: var(--font-1);
}

.disabled {
  cursor: not-allowed;
}

/* Add any additional styles based on your design */
</style>
