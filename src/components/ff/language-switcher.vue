<template>
  <div>
    <button @click="switchLanguage('en')">English</button>
    <button @click="switchLanguage('fr')">Français</button>
  </div>
</template>

<script setup lang="ts">
import { useLanguageSwitcher } from '~/composables/useLanguageSwitcher';

// Get the `switchLocale` function from the composable
const { switchLocale } = useLanguageSwitcher();

// A method to call `switchLocale` with the new locale
const switchLanguage = (language: string) => {
  switchLocale(language);
};
</script>
