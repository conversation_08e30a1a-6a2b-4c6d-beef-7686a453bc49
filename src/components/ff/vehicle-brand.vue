<template>
  <template v-if="!data.make && Object.keys(data.make).length > 0">
    <img
      :src="getVehicleImageSrc(data.make)"
      class="mb-0.5 object-fit w-full h-full ff_vehicle_brand"
    />
  </template>
  <template v-else>
    <div class="mb-0.5 object-fit w-full h-full ff_vehicle_brand empty">
      <ff-icon icon="no-image" size="20" color="#bcbcbc"></ff-icon>
    </div>
  </template>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object,
    default: "",
  },
});

const getVehicleImageSrc = (value: Object) => {
  if (Object.keys(value).length > 0) {
    // Convert value name to lowercase
    // Remove spaces and convert to underscors
    const vehicleName = value.name.toLowerCase().replace(/\s+/g, "_");
    return `/car_logos/${vehicleName}.png`;
  }
  return "";
};
</script>

<style lang="scss" scoped>
.ff_vehicle_brand {
  max-width: 50px;
  border-radius: 0;
  margin-right: 11px;
  &.empty {
    background: #f1f1f1;
    min-width: 60px;
    margin-right: 0;
    height: 100%;
    background-size: cover;
    height: 40px;
    border-radius: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
