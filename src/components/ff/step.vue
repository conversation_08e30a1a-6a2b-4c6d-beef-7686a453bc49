<template>
  <div class="steps-container">
    <div
      v-for="(step, index) in steps"
      :key="step.id"
      class="step_item"
      :class="{ active: activeStepId == step.id }"
    >
      <!-- Step Number or Checkmark Icon -->
      <div class="step_indicator" :class="{ completed: step.is_completed }">
        <svg
          width="20"
          v-if="step.is_completed"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.6666 5L7.49992 14.1667L3.33325 10"
            stroke="#F9FAFB"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>

        <span v-else class="step-number">
          {{ index + 1 }}
        </span>
      </div>

      <!-- Step Title -->
      <p class="step-title">{{ $t("steps." + step.id) }}</p>
      <!-- You can adjust the title based on your step structure -->

      <!-- Separator, except for the last step -->
      <span v-if="index < steps.length - 1" class="separator">></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSteps } from "@store/user/account/c_useAccountStep";
const { steps, activeStepId, completeStep } = useSteps();
</script>

<style lang="scss" scoped>
// @import '@assets/scss/variables.scss';
.steps-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  max-width: fit-content;
  gap: 10px;
  .step_item {
    display: flex;
    align-items: center;
    gap: 10px;
    &.active {
      .step_indicator {
        background: var(--color--primary);
        border: 1px solid var(--color--primary);
        color: #fff;
      }
      .step-title {
        color: #000;
      }
    }
    p {
      font-size: 18px;
      font-family: var(--font-3);
      color: #c4c4c4;
      white-space: nowrap;

      font-weight: 500;
    }
    .step_indicator {
      border: 1px solid #dedfe3;
      color: #9e9e9e;
      font-size: 14px;
      font-weight: bold;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50px;
      font-family: var(--font-3);
      .step-number {
      }
      .icon {
        &.checkmark {
          background: #000;
        }
      }
      &.completed {
        background: var(--color--primary);
      }
    }
  }
}
</style>
