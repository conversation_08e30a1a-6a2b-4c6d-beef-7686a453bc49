<!-- components/InsuranceOptions.vue -->
<template>
  <div class="flex items-start gap-1 py-[56px]">
    <div class="w-[30%] text-[14px]">
      <ff-icon :icon="icon" size="24" />
      <p>{{ title }}</p>
      <p>{{ description }}</p>
    </div>
    <div class="w-full flex flex-col gap-1">
      <div
        v-for="(option, index) in options"
        :key="index"
        class="border rounded-xl p-5 flex justify-between items-center"
        :class="[option.selected ? 'bg-gray-100' : '']"
      >
        <ff-switch
          :checked="option.selected"
          @change="() => toggleOption(option.title)"
        />
        <div class="px-5 flex-1">
          <h6>{{ option.title }}</h6>
          <p>{{ option.description }}</p>
        </div>
        <FfFlag v-if="option.country" :country="option.country" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from "vue";
import { useInsuranceStore } from "../../stores/user/features";
import { Option, Features } from "../../models/features";

const props = defineProps({
  type: {
    type: String as () => keyof Features,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
});

const insuranceStore = useInsuranceStore();

const options = computed(() => {
  return insuranceStore[props.type] as Option[];
});

const toggleOption = (title: string) => {
  insuranceStore.toggleOption(props.type, title);
};
</script>
