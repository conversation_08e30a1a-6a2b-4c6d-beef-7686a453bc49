<template>
  <div class="steps-vertical-container">
    <div class="steps-list-vertical">
      <div
        v-for="(step, index) in steps"
        :key="step.id"
        class="step_item_vertical"
        :class="{
          active: currentStep === step.id,
          completed: step.completed,
        }"
      >
        <!-- Step Indicator -->
        <div
          class="step_indicator_vertical"
          :class="{ completed: step.completed }"
        >
          <svg
            width="16"
            v-if="step.completed"
            height="16"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.6666 5L7.49992 14.1667L3.33325 10"
              stroke="#F9FAFB"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span v-else class="step-number">{{ index + 1 }}</span>
        </div>

        <!-- Step Content -->
        <div class="step-content">
          <h3 class="step-title">{{ step.title }}</h3>
          <p class="step-description" v-if="step.description">
            {{ step.description }}
          </p>
        </div>

        <!-- Connecting Line (except for last step) -->
        <div
          v-if="index < steps.length - 1"
          class="step-connector"
          :class="{ completed: step.completed }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Step {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
}

interface Props {
  steps: Step[];
  currentStep: string;
  showCard?: boolean;
}

withDefaults(defineProps<Props>(), {
  showCard: true,
});
</script>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;

.steps-vertical-container {
  width: 100%;
}

.steps-list-vertical {
  border-radius: 12px;
  
}

.step_item_vertical {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
    padding-top: 5px;
  }

  &.active {
    .step_indicator_vertical {
      background: transparent;
      border: 2px solid var(--color--primary, #3b82f6);
      color: var(--color--primary, #3b82f6);
      box-shadow: 0px 4px 8px 0px rgba(59, 130, 246, 0.2);
    }

    .step-title {
      color: var(--color--primary, #3b82f6);
      font-weight: 600;
    }

    .step-description {
      color: #374151;
    }

    // When step is both active and completed
    &.completed {
      .step_indicator_vertical {
        background: var(--color--primary, #3b82f6);
        border: 2px solid var(--color--primary, #3b82f6);
        color: white;
        box-shadow: 0px 4px 8px 0px rgba(59, 130, 246, 0.3);
      }

      .step-title {
        color: var(--color--primary, #3b82f6);
        font-weight: 600;
      }

      .step-description {
        color: #374151;
      }
    }
  }

  &.completed {
    .step_indicator_vertical {
      background: var(--color--primary, #3b82f6);
      border: 1px solid var(--color--primary, #3b82f6);
      color: white;
    }

    .step-connector {
      background: var(--color--primary, #3b82f6);
    }

    .step-title {
      color: #374151;
    }

    .step-description {
      color: #6b7280;
    }
  }

  .step_indicator_vertical {
    color: #6b7280;
    font-size: 14px;
    font-weight: bold;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    background: transparent;
    border: 1px solid #d1d5db;
    box-shadow: none;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
    transition: all 0.2s ease;

    &.completed {
      background: var(--color--primary, #3b82f6);
      border: 1px solid var(--color--primary, #3b82f6);
      color: white;
    }

    .step-number {
      font-weight: 600;
    }
  }

  .step-content {
    flex: 1;
    padding-top: 2px;

    .step-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 0.5rem;
      line-height: 1.4;
      transition: all 0.2s ease;
    }

    .step-description {
      font-size: 0.95rem;
      color: #6b7280;
      line-height: 1.5;
      transition: all 0.2s ease;
    }
  }

  .step-connector {
    position: absolute;
    left: 17px;
    top: 45px;
    bottom: -32px;
    width: 2px;
    background: #e5e7eb;
    z-index: 1;
    transition: all 0.2s ease;

    &.completed {
      background: var(--color--primary, #3b82f6);
    }
  }
}

// Responsive design
@include respond-below(sm) {


  .steps-list-vertical {
          padding: 0;
  }

  .step_item_vertical {
    gap: 0.75rem;
    margin-bottom: 1.5rem;

    .step_indicator_vertical {
      width: 30px;
      height: 30px;
      font-size: 12px;
    }

    .step-content {
      .step-title {
        font-size: 1rem;
      }

      .step-description {
        font-size: 0.875rem;
      }
    }

    .step-connector {
      left: 14px;
      top: 40px;
      bottom: -24px;
    }
  }
}
</style>
