<template>
  <div class="steps-container">
    <div
      v-for="(step, index) in options"
      :key="index"
      class="step_item"
      :class="{ active: current == step.value }"
    >
      <template v-if="!isLine">
        <!-- Step Number or Checkmark Icon -->
        <transition name="step-fade">
          <div class="step_indicator" :class="{ completed: step.completed }">
            <svg
              width="20"
              v-if="step.completed"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16.6666 5L7.49992 14.1667L3.33325 10"
                stroke="#F9FAFB"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>

            <span v-else class="step-number">
              {{ index + 1 }}
            </span>
          </div>
        </transition>

        <!-- Step Title -->
        <transition name="step-fade">
          <p class="step-title">
            {{ $t("tabs.add_edit_vehicle." + step.value) }}
          </p>
        </transition>
      </template>
      <template v-else>
        <div class="lineItemContent"></div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Step {
  value: string;
  completed: boolean;
  active: boolean;
  can_be_activated: boolean;
}

const props = defineProps<{
  current: string;
  options: Step[];
  isLine: boolean;
}>();
</script>

<style lang="scss" scoped>
@import "@assets/scss/variables.scss";
.steps-container {
  display: flex;
  align-items: center;
  max-width: 100%;
  width: 100%;
  justify-content: space-between;

  .step_item {
    display: flex;
    align-items: center;
    gap: 10px;
    &:after {
      content: "";
      position: relative;
      background: #e5e7eb;
      height: 1px;
      width: 120px;
    }
    &:last-child {
      &:after {
        display: none;
      }
    }

    &.active {
      .step_indicator {
        background: var(--color--primary);
        border: 1px solid var(--color--primary);
        color: #fff;
      }
      .step-title {
        color: var(--color--primary);
      }
      &:after {
        background-color: var(--color--primary);
      }
    }
    p {
      font-size: 18px;
      font-family: var(--font-3);
      color: #1f2937;
      white-space: nowrap;

      font-weight: 500;
    }
    .step_indicator {
      color: #1f2937;
      font-size: 14px;
      font-weight: bold;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      padding: 5px;
      font-family: var(--font-3);
      background: #f3f4f6;
      /* Drop Shadow/sm */
      box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
      .step-number {
      }
      .icon {
        &.checkmark {
          background: #000;
        }
      }
      &.completed {
        background: var(--color--primary);
      }
    }
  }
}
</style>
