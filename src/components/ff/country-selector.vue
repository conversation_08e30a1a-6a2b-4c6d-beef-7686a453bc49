<template>
  <div class="ff_select">
    <div class="ff_select_container">
      <label v-if="label">{{ label }}</label>
      <div class="ff_select_input" tabindex="0" @click="toggleDropdown">
        <template v-if="leftIcon">
          <div class="left_icon">
            <ff-icon :icon="leftIcon" size="24px" color="inherit" />
          </div>
        </template>

        <div class="ff_select_input_text">
          <template v-if="selectedOption">
            <template v-if="hasFlag">
              <ff-flag :country="selectedOption?.code" size="sm" />
            </template>

            <p>{{ selectedOption?.name }}</p>
          </template>
          <template v-else>
            <p class="placeholder">{{ placeholder }}</p>
          </template>
        </div>

        <div class="right_icon">
          <ff-icon
            icon="chevron-down"
            color="#667085"
            size="18px"
            v-if="!dropdownOpen"
          />
          <ff-icon
            icon="chevron-up"
            color="#667085"
            size="18px"
            v-if="dropdownOpen"
          />
        </div>
      </div>

      <div class="input_errors" v-if="errorMessage">
        <p class="error">{{ errorMessage }}</p>
      </div>

      <div
        class="ff_dropdown_container"
        v-if="dropdownOpen"
        ref="dropdownContainer"
      >
        <div class="ff_dropdown_container_mobile">
          <div class="dropdown_container_mobile_message">
            <template v-if="label">
              <p>{{ label }}</p>
            </template>
            <template v-else>
              <p>{{ placeholder }}</p>
            </template>
          </div>
        </div>
        <template v-if="options.values.length > 5">
          <!-- Show input to search tthrough options and filter things below -->
          <div class="dropdown_search">
            <!-- on enter if there are only one select the only value -->
            <!-- <input
              ref="searchInput"
              type="text"
              placeholder="Search"
              v-model="searchQuery"
              @keydown.enter="selectOption(filteredOptions[0])"
            /> -->
          </div>
        </template>

        <div class="ff_dropdown_list">
          <template v-if="filteredOptions.length > 0">
            <div
              class="ff_dropdown_item"
              v-for="(option_item, index) in filteredOptions"
              :key="index"
              @click="selectOption(option_item)"
            >
              <div class="ff_dropdown_item_content">
                <template v-if="hasFlag">
                  <ff-flag :country="option_item.code" size="sm" />
                </template>
                <p>
                  {{ option_item.name }}
                </p>
                <ff-icon icon="check" v-if="isSelected(option_item)" />
              </div>
            </div>
          </template>
        </div>
      </div>
      <div
        class="ff_dropdown_backdrop"
        v-if="dropdownOpen"
        @click="toggleDropdown"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// import citiesData from "@/assets/kosovo-cities.json";
import { onClickOutside } from "@vueuse/core";

import { usePublic } from "@services/usePublic";
const { get_countries, get_city_by_country } = usePublic();
import { Country } from "@models/app";

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: "",
  },
  hasFlag: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "Select option",
  },
  leftIcon: String,
  label: String,
  type: {
    type: String,
    default: "text",
  },
  id: String,
  name: String,
  disabled: Boolean,
  errorMessage: String,
  labelSelector: {
    type: String,
    default: "label",
  },
  translateStringPrefix: {
    type: String,
    default: "",
  },
  translateTarget: {
    type: String,
    default: "",
  },
  valueSelector: {
    type: String,
    default: "id",
  },
});

const emit = defineEmits(["update:modelValue", "change"]);
const options = ref(<Country[]>[]);
const internalValue = ref(props.modelValue);
const dropdownOpen = ref(false);
const searchQuery = ref("");
const selectedOption = ref<Country | null>(null);
const dropdownContainer = ref(null);
// Refs
const searchInput = ref<HTMLInputElement | null>(null);

// Computed property to filter options based on search query
const filteredOptions = computed(() => {
  if (!searchQuery.value) {
    return options.value;
  }
  return options.value.filter((option: Country) => {
    const label = option.name.toString().toLowerCase();
    return label.includes(searchQuery.value.toLowerCase());
  });
});

const getLabelFromValue = computed(() => {
  return options.value.find((option: Country) => option.id === props.modelValue)
    ?.name;
});

const selectOption = (option: Country) => {
  if (!option) return;
  selectedOption.value = option;
  internalValue.value = option.id;
  emit("update:modelValue", option.id);
  emit("change", option);
  closeDropdown();
};

onClickOutside(dropdownContainer, (event) => toggleDropdown());

const isSelected = (option: Country) => {
  return selectedOption.value && selectedOption.value.id === option.id;
};

const closeDropdown = () => {
  dropdownOpen.value = false;
};
const toggleDropdown = () => {
  dropdownOpen.value = !dropdownOpen.value;
  if (dropdownOpen.value) {
    document.body.classList.add("no-scroll");
  } else {
    document.body.classList.remove("no-scroll");
  }
  if (dropdownOpen.value) {
    if (options.value.length > 5) {
      nextTick(() => {
        searchInput.value?.focus();
      });
    }
    if (window.innerWidth < 886) {
      document.body.classList.add("no-scroll");
    }
  } else {
    document.body.classList.remove("no-scroll");
  }
};

// Automatically select the option if modelValue matches on load
const setSelectedOption = (value: string | number) => {
  const matchedOption = options.value.find((option) => option.id === value);
  if (matchedOption) {
    selectedOption.value = matchedOption;
    internalValue.value = matchedOption.name;
    emit("change", matchedOption);
    closeDropdown();
  }
};

watch(
  () => props.modelValue,
  (newValue) => {
    internalValue.value = newValue;
    setSelectedOption(newValue);
  },
  { immediate: true } // This ensures the watcher is called immediately on load
);

const getCountries = () => {
  try {
    get_countries({ list_for_vehicle_registration: true }).then(
      (response: { data: Country[] }) => {
        if (response.data) {
          options.value = response.data;
          setSelectedOption(props.modelValue);
        }
      }
    );
  } catch (error) {
    console.log(error);
  }
};
onMounted(async () => {
  await getCountries();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";
.input-with-select {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.selected-option {
  color: var(
    --color--third
  ); /* Change this to the desired color for the selected option */
}

.placeholder-selected {
  color: var(
    --color--third
  ); /* Change this to the desired color for the selected placeholder */
}

.input_errors {
  margin-top: 4px;
}

.ff_select {
  .ff_select_container {
    position: relative;
    label {
      font-family: var(--font-2);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      color: #000;

      line-height: 37px;
    }
    .ff_select_input {
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--Colors-Border-border-primary, #d0d5dd);
      background: var(--Colors-Background-bg-primary, #fff);

      /* Shadows/shadow-sm */
      box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
      display: flex;
      padding: 10px 14px;
      align-items: center;
      gap: var(--spacing-md, 8px);
      align-self: stretch;
      .left_icon {
        .ff-icon {
          svg {
            stroke: #667085;
          }
        }
      }
      .ff_select_input_text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        flex: 1 0 0;
        display: flex;
        gap: 10px;
        align-items: center;
        p {
          color: #000;
          &.placeholder {
            color: #667085;
          }
        }
      }
      cursor: pointer;
      &:focus {
        border-radius: var(--radius-md, 8px);
        border: 1px solid var(--color--primary-lighter-v2);
        background: var(--Colors-Background-bg-primary, #fff);

        /* Focus rings/ring-brand-shadow-sm */
        box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05),
          0px 0px 0px 4px var(--color--primary-lighter);
      }
    }

    .ff_dropdown_backdrop {
      position: fixed;
      background: rgba(0, 0, 0, 0.15);
      height: 100%;
      width: 100%;
      z-index: 99;
      top: 0;
      bottom: 0;
      display: none;
      left: 0;
      @include respond-below(sm) {
        display: block;
      }
    }

    .ff_dropdown_container {
      display: flex;
      width: 100%;
      height: auto;
      align-items: flex-start;
      position: absolute;
      flex-direction: column;
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--Colors-Border-border-secondary, #eaecf0);
      background: #fff;

      /* Shadows/shadow-lg */
      box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
        0px 4px 6px -2px rgba(16, 24, 40, 0.03);
      z-index: 9;
      .dropdown_search {
        padding: 11px;
        padding-left: 11px;
        background: #f1f1f1;
        width: 100%;
        margin-top: -5px;
        padding-left: 11px;
        background: none;
        border-bottom: 1px solid #f1f1f1;
        input {
          border-radius: var(--radius-md, 8px);
          border: 1px solid var(--Colors-Border-border-secondary, #eaecf0);
          width: 100%;
          padding: 11px;
          &:focus {
            border-color: 000;
            outline: none;
          }
        }
      }
      @include respond-below(sm) {
        position: fixed;
        bottom: 0;
        border-radius: 0;
        z-index: 999;
        max-width: initial;
        width: 100%;
        left: 0;
      }
      .ff_dropdown_container_mobile {
        display: none;
        width: 100%;
        @include respond-below(sm) {
          display: block;
        }
        .dropdown_container_mobile_message {
          padding-left: 20px;
          margin: 20px 0;
          p {
            font-size: 14px;
            color: #667085;
          }
          display: none;
          @include respond-below(sm) {
            display: block;
          }
        }
      }

      .ff_dropdown_list {
        width: 100%;
        max-height: 320px;
        overflow: auto;
        .ff_dropdown_item {
          margin: 5px 0;
          display: flex;
          z-index: 99999999;
          align-items: center;
          align-self: stretch;
          cursor: pointer;

          width: 100%;
          padding-left: 5px;
          padding-right: 5px;
          margin-bottom: 5px;

          &:hover {
            .ff_dropdown_item_content {
              background: #efefef;
            }
          }
          &.selected {
            .ff_dropdown_item_content {
              background: #efefef;
            }
          }
          .ff_dropdown_item_content {
            display: flex;
            border-radius: 6px;
            padding: 8px var(--spacing-sm, 16px);
            align-items: center;
            align-self: stretch;
            cursor: pointer;
            width: 100%;
            gap: 10px;
            p {
              display: flex;
              align-items: center;
              gap: var(--spacing-md, 8px);
              flex: 1 0 0;
              color: #101828;
            }
            @include respond-below(sm) {
              padding: 22px 16px;
            }
          }
        }
      }
    }
  }
}
</style>
