<template>
  <div class="logo-container">
    <img
      :src="logoSrc"
      :width="logoWidth"
      :height="logoHeight"
      :alt="alt"
      :class="classes"
      :style="{
        width: logoWidth + 'px',
        height: logoHeight === 'auto' ? 'auto' : logoHeight + 'px',
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Props {
  src?: string;
  size?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl";
  width?: string | number;
  height?: string | number;
  alt?: string;
  classes?: string;
  isIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  src: "/logo-full.svg",
  size: "xl",
  alt: "Logo",
  classes: "",
  isIcon: false,
});

// Size mapping
const sizeMap = {
  xs: 24,
  sm: 32,
  md: 48,
  lg: 64,
  xl: 80,
  "2xl": 96,
};

// Computed source - use logo.svg when isIcon is true, otherwise use provided src
const logoSrc = computed(() => {
  if (props.isIcon) {
    return "/logo-icon.svg";
  }
  return props.src;
});

// Computed width - use custom width if provided, otherwise use size mapping
const logoWidth = computed(() => {
  if (props.width) {
    return props.width;
  }
  return sizeMap[props.size];
});

// Computed height - use custom height if provided, otherwise auto
const logoHeight = computed(() => {
  if (props.height) {
    return props.height;
  }
  return "auto";
});
</script>

<style scoped lang="scss">
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

img {
  display: block;
  max-width: none; /* Allow image to use specified width */
  height: auto;
  object-fit: contain; /* Maintain aspect ratio */
}
</style>
