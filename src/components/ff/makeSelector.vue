<template>
  <div v-if="type == 'select'">
    <ff-select
      v-model="selectedValue"
      :options="options"
      :error-message="errorMessage"
      labelSelector="name"
      idSelector="id"
      hasSelectImage="logo"
      @update:value="UpdateSelectValue"
    />
  </div>
  <div v-else-if="type == 'list'" class="listing-list-container">
    <template v-if="options.length > 0">
      <div class="list">
        <div
          class="listing-list-item"
          v-for="(make, index) in options"
          :key="index"
          @click="select_make_from_list(make)"
          :class="{ active: value && value.id == make.id }"
        >
          <ff-icon
            icon="checkmark"
            v-if="value && value.id == make.id"
            class="checked_icon"
            color="inherit"
          ></ff-icon>
          <img :src="make.logo" />
          <p>{{ make.name }}</p>
        </div>
      </div>
    </template>
  </div>

  <div class="input_errors" v-if="errorMessage">
    <p class="error">{{ $t("validations." + errorMessage) }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElaDropdownClasses } from "@/utilities/PrimeVueClasses";
const componentClasses = ref({ ...ElaDropdownClasses });

const type = ref("select");
const optionsLoading = ref(false);
const selectedCountry = ref();
const options = ref([]);
const { get_vehicle_makes } = useVehicles();

// onActivated(() => {

//   load_options();
// });

onMounted(() => {
  load_options();
});

const props = defineProps({
  value: Object,
  errorMessage: String,
});
const emits = defineEmits(["update:value", "listCarSelected"]);

const selectedValue = ref(props.value || "");

function updateModelValue(newValue: any) {
  emits("update:value", newValue.value);
}

const UpdateSelectValue = (e) => {
  console.log(e);
};
const load_options = async () => {
  optionsLoading.value = true; // Start loading

  try {
    let response = await get_vehicle_makes({});
    if (response.data.success) {
      options.value = response.data.data;
    }
  } catch (err) {
    // notify(handleError(err), "error", 5000);
  } finally {
    optionsLoading.value = false; // End loading
  }
};

const select_make_from_list = async (e) => {
  emits("listCarSelected", e);
};
</script>

<style lang="scss"></style>
