<template>
  <span
    class="ff_chip"
    :class="[
      'py-1 px-1.5 inline-flex items-center gap-x-1 text-xs font-medium rounded-full chip',
      typeClass,
    ]"
  >
    <template v-if="icon">
      <ff-icon :icon="icon" size="14" color="inherit"></ff-icon>
    </template>
    <template v-if="dot">
      <div class="dot"></div>
    </template>
    {{ chipContent }}
  </span>
</template>

<script setup lang="ts">
interface Props {
  chipContent?: string;
  type: string;
  size?: string;
  icon?: string;
  dot?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  chipContent: "",
  type: "primary",
  size: "md",
});

const styles: any = {
  none: "",
  success: "success",
  orange: "orange",
  danger: "danger",
  draft: "draft",
};

const typeClass = computed(() => styles[props.type]);
</script>

<style lang="scss">
// Assuming you have a .scss file where you define these
.ff_chip {
  &.none {
    // Default styles or empty if none needed
  }
  &.success {
    background-color: #ccfbf1; // bg-teal-100 in Tailwind
    color: #065f46; // text-teal-800 in Tailwind
    .dot {
      background: #3c9e19;
      // Blink
      animation: blink 1s linear infinite;
    }
  }
  &.orange {
    background-color: #ffedd5; // bg-orange-100 in Tailwind
    color: #c2410c; // text-orange-800 in Tailwind
    .dot {
      background: #ff5757;
      // Blink
      animation: blink 1s linear infinite;
    }
  }
  &.danger {
    background-color: #fee2e2; // bg-red-100 in Tailwind
    color: #991b1b; // text-red-800 in Tailwind
    .dot {
      background: #b9201f;
      // Blink
      animation: blink 1s linear infinite;
    }
  }
  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
</style>
