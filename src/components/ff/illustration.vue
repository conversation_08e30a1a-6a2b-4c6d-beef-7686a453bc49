<template>
  <div
    class="ff_illustration"
    :class="{
      'has-loader': icon == 'spinner',
      'is-bigger': type == 'pricing-schedule',
    }"
  >
    <template v-if="type == 'team_member'">
      <img src="/img/illustrations/01.svg" />
    </template>
    <template v-if="type == 'cloud'">
      <img src="/img/illustrations/01.svg" />
    </template>
    <template v-if="type == 'pricing-schedule'">
      <img src="/img/illustrations/03.svg" />
    </template>
    <template v-if="type == 'files'">
      <img src="/img/illustrations/02.svg" />
    </template>
    <div v-if="icon" class="icon_holder">
      <ff-icon :icon="icon" size="30" color="#fff"></ff-icon>
    </div>
  </div>
</template>

<script lang="ts" setup> 
const props = defineProps({
  type: String,
  icon: String,
});

watch(
  () => props.icon,
  (newVal, oldVal) => {
    console.log(`Icon changed from ${oldVal} to ${newVal}`);
  }
);
</script>

<style lang="scss" scoped>
.ff_illustration {
  margin-bottom: 40px;
  position: relative;
  display: flex;
  justify-content: center;
  &.has-loader {
    .icon_holder {
      background: #fff !important;
      backdrop-filter: none;
    }
  }
  &.is-bigger {
    margin-bottom: 0;
  }
  .icon_holder {
    position: absolute;
    display: flex;
    width: 28px;
    height: 28px;
    padding: 25px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 9999px;
    background: rgba(52, 64, 84, 0.2);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    left: 0;
    right: 0;
    margin: -61px auto;
    bottom: 52px;
  }
}
</style>
