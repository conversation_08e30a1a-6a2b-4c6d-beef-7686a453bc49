<template>
  <div class="ff_select">
    <div class="ff_select_container">
      <template v-if="labelPopover">
        <div class="ff_label_with_action">
          <label :for="id" v-if="label">{{ label }}</label>

          <div
            :class="`popoverContent hs-tooltip [--trigger:hover] ${selectedPopoverStyle} inline-block`"
          >
            <ff-icon
              icon="info"
              color="inherit"
              class="hs-tooltip-toggle cursor-pointer"
            />
            <div
              class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-10 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:top-0 after:-start-4 after:w-4 after:h-full"
              role="tooltip"
            >
              <slot name="popover"></slot>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <label v-if="label">{{ label }}</label>
      </template>
      <div
        class="ff_select_input focus:ring-primary_lighter"
        :class="{ disabled: disabled }"
        tabindex="0"
        @click="toggleDropdown"
      >
        <template v-if="leftIcon">
          <div class="left_icon">
            <ff-icon :icon="leftIcon" size="24px" color="inherit" />
          </div>
        </template>
        <div class="ff_select_input_text">
          <template v-if="internalValue && !isEmptyObject(internalValue)">
            <template v-if="hasFlag">
              <ff-flag :country="internalValue" size="sm" />
            </template>
            <template v-if="hasSelectImage">
              <img
                :src="getVehicleImageSrc(internalValue)"
                class="select_item_image"
              />
            </template>
            <template v-if="isIcon">
              <ff-icon :icon="internalValue[valueSelector]" size="24" />
            </template>
            <template v-if="translateStringPrefix">
              <p>
                <template v-if="typeof internalValue === 'object'">
                  {{
                    $t(translateStringPrefix + internalValue[translateTarget])
                  }}
                </template>
                <template v-else>
                  {{ $t(translateStringPrefix + internalValue) }}
                </template>
              </p>
            </template>
            <template v-else>
              <template v-if="typeof internalValue === 'object'">
                <p>{{ internalValue[labelSelector] }}</p>
              </template>
              <template v-else>
                <p>{{ getLabelFromValue }}</p>
              </template>
            </template>
          </template>
          <template v-else>
            <p class="placeholder">{{ placeholder }}</p>
          </template>
        </div>

        <div class="right_icon">
          <ff-icon
            icon="chevron-down"
            color="#667085"
            size="18px"
            v-if="!dropdownOpen && !optionsLoading"
          />
          <ff-icon
            icon="chevron-up"
            color="#667085"
            size="18px"
            v-if="dropdownOpen && !optionsLoading"
          />
          <ff-loader size="sm" v-if="optionsLoading" />
        </div>
      </div>

      <div class="input_errors" v-if="errorMessage">
        <p class="error">{{ errorMessage }}</p>
      </div>

      <div
        class="ff_dropdown_container"
        v-if="dropdownOpen"
        ref="dropdownContainer"
      >
        <div class="ff_dropdown_container_mobile">
          <div class="dropdown_container_mobile_message">
            <template v-if="label">
              <p>{{ label }}</p>
            </template>
            <template v-else>
              <p>{{ placeholder }}</p>
            </template>
          </div>
        </div>
        <!-- <template v-if="options.length > 5">
          <div class="dropdown_search">
            <input
              ref="searchInput"
              type="text"
              placeholder="Search"
              v-model="searchQuery"
              @keydown.enter="selectOption(filteredOptions[0])"
            />
          </div>
        </template> -->
        <div class="suggestions-dropdown">
          <template v-if="filteredOptions.length > 0">
            <div
              class="suggestion-item"
              v-for="(option_item, index) in filteredOptions"
              :key="index"
              @click="selectOption(option_item)"
            >
              <div class="ff_dropdown_item_content">
                <template v-if="hasFlag">
                  <ff-flag :country="option_item[valueSelector]" size="sm" />
                </template>
                <template v-if="hasSelectImage">
                  <img
                    :src="getVehicleImageSrc(option_item)"
                    class="select_item_image"
                  />
                </template>
                <template v-if="isIcon">
                  <ff-icon :icon="option_item[valueSelector]" size="24" />
                </template>
                <template v-if="translateStringPrefix">
                  <p>
                    {{
                      $t(translateStringPrefix + option_item[translateTarget])
                    }}
                  </p>
                </template>
                <template v-else>
                  <p>
                    {{ option_item[labelSelector] }}
                  </p>
                </template>
                <ff-icon icon="check" v-if="isSelected(option_item)" />
              </div>
            </div>
          </template>
        </div>
      </div>
      <div
        class="ff_dropdown_backdrop"
        v-if="dropdownOpen"
        @click="toggleDropdown"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { onClickOutside } from "@vueuse/core";

// Simple helper function
const isEmptyObject = (obj: any) => obj && Object.keys(obj).length === 0;

// Type definition
interface Id_Label_Interface {
  id: string | number;
  label: string;
  [key: string]: any;
}
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: "",
  },
  receiveType: {
    type: String,
    required: false,
    default: "value",
  },
  optionsLoading: {
    type: Boolean,
    default: false,
  },
  hasSelectImage: {
    type: String,
    default: false,
    required: false,
  },
  hasFlag: {
    type: Boolean,
    default: false,
  },
  labelPopover: Boolean,
  popoverPosition: {
    type: String,
    default: "top",
    required: false,
  },
  options: {
    type: Array as () => Id_Label_Interface[],
    default: () => [],
  },
  isIcon: Boolean,
  placeholder: {
    type: String,
    default: "Select option",
  },
  leftIcon: String,
  label: String,
  type: {
    type: String,
    default: "text",
  },
  id: String,
  name: String,
  disabled: Boolean,
  errorMessage: String,
  labelSelector: {
    type: String,
    default: "label",
  },
  translateStringPrefix: {
    type: String,
    default: "",
  },
  translateTarget: {
    type: String,
    default: "",
  },
  valueSelector: {
    type: String,
    default: "id",
  },
});

const popoverPositions = {
  right: "sm:[--placement:top] lg:[--placement:right]",
  left: "sm:[--placement:top] lg:[--placement:left]",
  top: "sm:[--placement:top] lg:[--placement:top]",
  bottom: "sm:[--placement:top] lg:[--placement:bottom]",
};

const selectedPopoverStyle = computed(
  () => popoverPositions[props.popoverPosition]
);

const emit = defineEmits(["update:modelValue", "change"]);

const getVehicleImageSrc = (value: Object) => {
  if (Object.keys(value).length > 0) {
    // Convert value name to lowercase
    // Remove spaces and convert to underscors
    const vehicleName = value.name.toLowerCase().replace(/\s+/g, "_");
    return `/car_logos/${vehicleName}.png`;
  }
  return "";
};
const internalValue = ref(props.modelValue);
const dropdownOpen = ref(false);
const searchQuery = ref("");
const selectedOption = ref<Id_Label_Interface | null>(null);
const dropdownContainer = ref(null);
// Refs
const searchInput = ref<HTMLInputElement | null>(null);

// Computed property to filter options based on search query
const filteredOptions = computed(() => {
  if (!searchQuery.value) {
    return props.options;
  }
  return props.options.filter((option: Id_Label_Interface) => {
    const label = option[props.labelSelector].toString().toLowerCase();
    return label.includes(searchQuery.value.toLowerCase());
  });
});

const getLabelFromValue = computed(() => {
  return props.options.find(
    (option: Id_Label_Interface) =>
      option[props.valueSelector] === props.modelValue
  )?.label;
});

const selectOption = (option: Id_Label_Interface) => {
  if (!option) return;
  selectedOption.value = option;
  internalValue.value = option[props.valueSelector];
  if (props.receiveType == "all") {
    emit("update:modelValue", option);
  } else if (props.receiveType == "value") {
    emit("update:modelValue", option[props.valueSelector]);
  }

  emit("change", option);
  toggleDropdown();
};

onClickOutside(dropdownContainer, (event) => toggleDropdown());

const isSelected = (option: Id_Label_Interface) => {
  console.log(option);
  return selectedOption.value && selectedOption.value.id === option.id;
};

const toggleDropdown = () => {
  if (props.disabled) return;
  dropdownOpen.value = !dropdownOpen.value;
  if (dropdownOpen.value) {
    document.body.classList.add("no-scroll");
  } else {
    document.body.classList.remove("no-scroll");
  }
  if (dropdownOpen.value) {
    if (props.options.length > 5) {
      nextTick(() => {
        searchInput.value?.focus();
      });
    }
    if (window.innerWidth < 886) {
      document.body.classList.add("no-scroll");
    }
  } else {
    document.body.classList.remove("no-scroll");
  }
};

// Automatically select the option if modelValue matches on load
const setSelectedOption = (value: string | number) => {
  if (props.options.length > 0) {
    const matchedOption = props.options.find(
      (option) => option.label === value
    );
    console.log(props.options);
    if (matchedOption) {
      selectedOption.value = matchedOption;
      internalValue.value = matchedOption.label;
    }
  }
};

watch(
  () => props.modelValue,
  (newValue) => {
    internalValue.value = newValue;
    setSelectedOption(newValue);
  },
  { immediate: true } // This ensures the watcher is called immediately on load
);

onMounted(() => {
  setSelectedOption(props.modelValue);
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.hs-tooltip-toggle {
  width: 30px;
  text-align: right;
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: center;
  padding: 5px;
}
.input-with-select {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.selected-option {
  color: var(
    --color--third
  ); /* Change this to the desired color for the selected option */
}

.placeholder-selected {
  color: var(
    --color--third
  ); /* Change this to the desired color for the selected placeholder */
}

.input_errors {
  margin-top: 4px;
}

.ff_select {
  .ff_select_container {
    position: relative;
    label {
      font-family: var(--font-2);
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      color: #000;

      line-height: 37px;
    }
    .ff_select_input {
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--Colors-Border-border-primary, #d0d5dd);
      background: var(--Colors-Background-bg-primary, #fff);

      /* Shadows/shadow-sm */
      box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
      display: flex;
      padding: 10px 14px;
      align-items: center;
      gap: var(--spacing-md, 8px);
      align-self: stretch;
      &.disabled {
        background: #f1f1f1 !important;
        cursor: not-allowed;
        &:focus,
        &:active {
          border-color: #d0d5dd !important;
          outline: none;
          box-shadow: none;
          user-select: none;
        }
      }
      .left_icon {
        .ff-icon {
          svg {
            stroke: #667085;
          }
        }
      }
      .ff_select_input_text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        flex: 1 0 0;
        display: flex;
        gap: 10px;
        align-items: center;
        .select_item_image {
          max-width: 30px;
          min-width: 30px;
          object-fit: contain;
        }
        p {
          color: #000;
          &.placeholder {
            color: #667085;
          }
        }
      }
      cursor: pointer;
      &:focus {
        border-radius: var(--radius-md, 8px);
        border: 1px solid var(--color--primary-lighter-v2);
        background: var(--Colors-Background-bg-primary, #fff);

        /* Focus rings/ring-brand-shadow-sm */
        box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05),
          0px 0px 0px 4px var(--color--primary-lighter);
      }
    }

    .ff_dropdown_backdrop {
      position: fixed;
      background: rgba(0, 0, 0, 0.15);
      height: 100%;
      width: 100%;
      z-index: 99;
      top: 0;
      bottom: 0;
      display: none;
      left: 0;
      @include respond-below(sm) {
        display: block;
      }
    }

    .ff_dropdown_container {
      display: flex;
      width: 100%;
      height: auto;
      align-items: flex-start;
      position: absolute;
      flex-direction: column;
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--Colors-Border-border-secondary, #eaecf0);
      background: #fff;

      /* Shadows/shadow-lg */
      box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
        0px 4px 6px -2px rgba(16, 24, 40, 0.03);
      z-index: 9;
      .dropdown_search {
        padding: 11px;
        padding-left: 11px;
        background: #f1f1f1;
        width: 100%;
        margin-top: -5px;
        padding-left: 11px;
        background: none;
        border-bottom: 1px solid #f1f1f1;
        box-sizing: border-box;
        input {
          border-radius: var(--radius-md, 8px);
          border: 1px solid var(--Colors-Border-border-secondary, #eaecf0);
          width: 100%;
          padding: 11px;
          &:focus {
            border-color: 000;
            outline: none;
          }
        }
      }
      @include respond-below(sm) {
        position: fixed;
        bottom: 0;
        border-radius: 0;
        z-index: 999;
        max-width: initial;
        width: 100%;
        left: 0;
      }
      .ff_dropdown_container_mobile {
        display: none;
        width: 100%;
        @include respond-below(sm) {
          display: block;
        }
        .dropdown_container_mobile_message {
          padding-left: 20px;
          margin: 20px 0;
          p {
            font-size: 14px;
            color: #667085;
          }
          display: none;
          @include respond-below(sm) {
            display: block;
          }
        }
      }

      .ff_dropdown_list {
        width: 100%;
        max-height: 320px;
        overflow: auto;
        .ff_dropdown_item {
          margin: 5px 0;
          display: flex;
          z-index: 99999999;
          align-items: center;
          align-self: stretch;
          cursor: pointer;

          width: 100%;
          padding-left: 5px;
          padding-right: 5px;
          margin-bottom: 5px;
          box-sizing: border-box;

          &:hover {
            .ff_dropdown_item_content {
              background: radial-gradient(
                117.79% 50% at 50% 50%,
                #ddfeef 0%,
                #f1f4ff 100%
              );
            }
          }
          &.selected {
            .ff_dropdown_item_content {
              background: radial-gradient(
                117.79% 50% at 50% 50%,
                #ddfeef 0%,
                #f1f4ff 100%
              );
            }
          }
          .ff_dropdown_item_content {
            display: flex;
            border-radius: 6px;
            padding: 8px var(--spacing-sm, 16px);
            align-items: center;
            align-self: stretch;
            cursor: pointer;
            width: 100%;
            gap: 10px;
            .select_item_image {
              max-width: 30px;
              min-width: 30px;
              object-fit: contain;
            }
            p {
              display: flex;
              align-items: center;
              gap: var(--spacing-md, 8px);
              flex: 1 0 0;
              color: #101828;
            }
            @include respond-below(sm) {
              padding: 22px 16px;
            }
          }
        }
      }
    }
  }
}
</style>
