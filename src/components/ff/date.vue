<template>
  <div :class="['ff_input', extraClass]">
    <label :for="id" v-if="label">{{ label }}</label>
    <div class="custom-date-input">
      <VueDatePicker 
      v-model="internalValue" 
      :auto-apply="true"
      :enable-time-picker="false"
      :uid="id"
      :placeholder="placeholderText"
      :disabled="disabled"
      :no-today="noToday"
      :readonly="readonly"
      :required="required"
      :name="id"
      :action-row="{ showNow: true, showPreview: false, showSelect: true, showCancel : true }"
      >
        <template #input-icon>
          <ff-icon v-if="showCalendarIcon" class="calendar-icon" icon="calendar"/>
        </template>
        <!-- <template #clear-icon="{ clear }">
            <img class="input-slot-image" src="/logo.png" @click="clear" />
        </template> -->
        <template #clock-icon>
            <img class="slot-icon" src="/logo.png"/>
        </template>
        <template #arrow-left>
            <img class="slot-icon" icon="left"  src="/logo.png"/>
        </template>
        <template #arrow-right>
            <img class="slot-icon" src="/logo.png"/>
        </template>
        <template #arrow-up>
            <img class="slot-icon" src="/logo.png"/>
        </template>
        <template #arrow-down>
            <img class="slot-icon" src="/logo.png"/>
        </template>
      </VueDatePicker>
      <!-- <input
        :autocomplete="autocomplete"
        type="date"
        :id="id"
        :value="internalValue"
        :placeholder="showPlaceholder ? placeholderText : ''"
        @input="updateValue"
        @blur="onInputBlur"
        @focus="onInputFocus"
        :disabled="disabled"
      /> -->
    </div>

    <div class="input_errors" v-if="errorMessage">
      <p class="error">{{ errorMessage }}</p>
    </div>
    <template v-else>
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
const props = defineProps({
  modelValue: String,
  label: String,
  id: String,
  placeholderText: String,
  disabled: Boolean,
  autocomplete: String,
  extraClass: String,
  errorMessage: String,
  hint: String,
  noToday: Boolean,
  readonly: Boolean,
  required: Boolean
})

const internalValue = ref(props.modelValue)
const showCalendarIcon = ref(true);

// Watch for changes in internalValue and update showCalendarIcon accordingly
watch(internalValue, () => {
  showCalendarIcon.value = !internalValue.value;
});

const showPlaceholder = ref(true)

const emit = defineEmits(['update:modelValue', 'blur'])

const updateValue = (event: Event) => {
  const newValue = (event.target as HTMLInputElement).value
  internalValue.value = newValue
  emit('update:modelValue', newValue)
}

const onInputFocus = () => {
  showPlaceholder.value = false
}

const onInputBlur = () => {
  showPlaceholder.value = !internalValue.value
}

watch(
  () => props.modelValue,
  (newValue) => {
    internalValue.value = newValue
    showPlaceholder.value = !newValue
  }
)
</script>

<style scoped>
.custom-date-input {
  position: relative;
}

/* Adjust the right position as needed */
.calendar-icon {
  position: absolute;
  left: 327px;
  top: 50%;
  transform: translateY(-50%);
}
.custom-date-input ::placeholder {
  color: #344054;
}
</style>