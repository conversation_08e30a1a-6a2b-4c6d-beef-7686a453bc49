<template>
  <!-- Table Section -->
  <div class="ff_table py-5 mx-auto">
    <!-- Card -->
    <div class="flex flex-col">
      <div>
        <div class="min-w-full inline-block align-middle">
          <div
            class="ff_table_container bg-white border border-gray-200 rounded-xl shadow-sm"
          >
            <!-- Header -->
            <div
              class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200"
            >
              <div class="ff_table_title">
                <slot name="tableTitle"></slot>
              </div>

              <div class="ff_table_actions">
                <template v-if="TableOptions.refresh">
                  <div class="inline-flex gap-x-2">
                    <ff-button
                      type="bordered"
                      size="md"
                      @click="tableOptionClicked('refresh')"
                      ><ff-icon
                        icon="refresh"
                        color="inherit"
                        size="20"
                      ></ff-icon
                      >{{ t("table.refresh") }}</ff-button
                    >
                  </div>
                </template>

                <slot name="tableActions"> </slot>
              </div>
            </div>

            <!-- End Header -->
            <template v-if="TableLoading">
              <ff-skeleton type="table"></ff-skeleton>
            </template>
            <template v-else>
              <table class="ff_table_start min-w-full divide-y divide-gray-200">
                <template v-if="TableData.length > 0">
                  <thead class="ff_table_thead bg-gray-50">
                    <tr class="ff_table_thead_tr">
                      <th
                        scope="col"
                        class="ps-6 py-3 text-start"
                        v-if="TableOptions.select"
                      >
                        <label for="hs-at-with-checkboxes-main" class="flex">
                          <input
                            type="checkbox"
                            class="shrink-0 border-gray-300 rounded text-primary focus:ring-primary_lighter disabled:opacity-50 disabled:pointer-events-none"
                            id="hs-at-with-checkboxes-main"
                          />
                          <span class="sr-only">Checkbox</span>
                        </label>
                      </th>
                      <th
                        scope="col"
                        class="py-2 text-start px-6"
                        v-for="(column_item, index) in TableColumns"
                      >
                        <div class="flex items-center gap-x-2">
                          <span
                            v-if="column_item.key !== 'actions'"
                            class="text-xs font-semibold uppercase tracking-wide text-gray-800"
                          >
                            {{ $t("table." + column_item.key) }}
                          </span>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="ff_table_tbody divide-y divide-gray-200">
                    <tr
                      v-for="(row, rowIndex) in TableData"
                      :key="rowIndex"
                      class="ff_table_tbody_tr bg-white hover:bg-gray-50"
                    >
                      <td
                        class="size-px whitespace-nowrap"
                        v-if="TableOptions.select"
                      >
                        <div class="ps-6 py-3">
                          <label for="hs-at-with-checkboxes-1" class="flex">
                            <input
                              type="checkbox"
                              class="shrink-0 border-gray-300 rounded text-primary focus:ring-primary_lighter disabled:opacity-50 disabled:pointer-events-none"
                              id="hs-at-with-checkboxes-1"
                            />
                            <span class="sr-only">Checkbox</span>
                          </label>
                        </div>
                      </td>

                      <td
                        v-for="(column_item, colIndex) in TableColumns"
                        :key="colIndex"
                        :data-col-label="$t('table.' + column_item.key)"
                        class="whitespace-nowrap"
                      >
                        <div class="px-6 py-3">
                          <slot
                            :name="'column-' + column_item.key"
                            :row="row"
                            :column="column_item"
                          >
                            <span>{{ row[column_item.key] }}</span>
                          </slot>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </template>
                <template v-else>
                  <slot name="tableNoResults"></slot>
                </template>
              </table>
            </template>
            <slot name="tablePagination"></slot>
          </div>
        </div>
      </div>
    </div>
    <!-- End Card -->
  </div>
  <!-- End Table Section -->
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
const { t } = useI18n();

interface TableOptions {
  select?: boolean;
  refresh?: boolean;
}

interface TableColumn {
  key: string;
  visible: boolean;
  sortable: boolean;
  has_select?: boolean;
}

interface TableRow {
  [key: string]: any;
}
const emits = defineEmits(["refresh", "add_new"]);

const tableOptionClicked = (option: "refresh" | "add_new") => {
  if (option == "refresh") {
    emits("refresh");
  }
};
const props = defineProps<{
  TableOptions: TableOptions;
  TableColumns: TableColumn[];
  TableLoading: boolean;
  TableData: TableRow[];
}>();
</script>

<style lang="scss">
.ff_table_container {
  overflow: hidden;
}
</style>
