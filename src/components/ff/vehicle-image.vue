<template>
  <nuxt-img
    class="mb-0.5 object-fit w-full h-full ff_vehicle_image"
    :src="src"
    format="webp"
    :key="src"
    alt="VEHICLEIMAGE"
    preload
    provider="s3"
    lazy
  />
</template>

<script setup lang="ts">
const props = defineProps({
  src: {
    type: String,
    default: "",
  },
});
</script>

<style scoped>
.ff_vehicle_image {
  max-width: 60px;
  border-radius: 3px;
  object-fit: cover;
  max-height: 38px;
}
</style>
