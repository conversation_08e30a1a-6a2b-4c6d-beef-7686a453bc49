<template>
  
  <template v-if="user?.avatarUrl">
    <img
      class="ff_avatar_img inline-block rounded-full object-cover border border-gray-200 shadow-sm"
      :class="computedSize"
      :src="user?.avatarUrl"
      alt="Profile Avatar"
    />
  </template>
  <template v-else>
    <span
      :class="`ff_avatar_prefix inline-flex items-center justify-center rounded-full bg-gradient-to-br from-gray-600 to-gray-700 text-white font-medium shadow-sm border border-gray-200 ${computedSize}`"
    >
      {{ fallbackInitials }}
    </span>
  </template>
</template>

<script setup lang="ts">
import { computed, type PropType } from "vue";
import type { User } from "@/stores/types/auth.types";

const props = defineProps({
  image: {
    type: String,
    required: false,
    default: "",
  },
  size: {
    type: String,
    required: false,
    default: "md",
  },
  fallback: {
    type: [String, Object],
    required: false,
    default: "Av",
  },
  user: {
    type: Object as PropType<User>,
    required: false,
    default: null,
  },
});

const computedSize = computed(() => {
  if (props.size === "xs") {
    return "w-4 h-4 text-xs";
  } else if (props.size === "sm") {
    return "w-8 h-8 text-sm";
  } else if (props.size === "md") {
    return "w-10 h-10 text-base";
  } else if (props.size === "lg") {
    return "w-12 h-12 text-lg";
  } else if (props.size === "xl") {
    return "w-16 h-16 text-xl";
  }
  return "w-10 h-10 text-base";
});

const fallbackInitials = computed(() => {
  // First try to get initials from user object
  if (props.user?.profile) {
    const firstName = props.user.profile.firstName;
    const lastName = props.user.profile.lastName;
    if (firstName && lastName) {
      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    }
    if (firstName) {
      return firstName.charAt(0).toUpperCase();
    }
  }

  // If fallback is a string, return it directly
  if (typeof props.fallback === "string") {
    return props.fallback.toUpperCase();
  }

  // If fallback is an object with name properties
  if (props.fallback && typeof props.fallback === "object") {
    if (props.fallback.firstName && props.fallback.lastName) {
      const initials =
        props.fallback.firstName.charAt(0) + props.fallback.lastName.charAt(0);
      return initials.toUpperCase();
    }
  }

  // Default fallback
  return "U";
});
</script>

<style lang="scss">

.ff_avatar_img{
  display:inline-flex;
  border-radius: 50px;
  object-fit: cover;
  border: 1px solid #f1f1f1;
  width: 32px;
  height: 32px;
}
</style>
