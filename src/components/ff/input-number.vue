<template>
  <div
    class="ff_input_number"
    :aria-disabled="disabled"
    aria-live="polite"
    :class="{ 'has-border': hasBorder }"
  >
    <template v-if="icon">
      <ff-icon :icon="icon" color="inherit" size="medium" />
    </template>
    <label v-if="label" :for="label">{{ label }}</label>
    <div class="ff_input_number_container" :class="{ disabled }">
      <ff-icon
        icon="minus"
        color="inherit"
        @click="decrease"
        aria-label="Decrease value"
        :aria-disabled="disabled"
      ></ff-icon>
      <div class="ff_input_number_input">
        {{ internalValue }}
      </div>
      <ff-icon
        icon="plus"
        color="inherit"
        @click="increase"
        aria-label="Increase value"
        :aria-disabled="disabled"
      ></ff-icon>
    </div>
    <div class="input_error" v-if="props.errorMessage || error.value">
      <p class="error">{{ props.errorMessage || error.value }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Id_Label_Interface } from "~/types";

/**
 * @typedef {Object} Props
 * @property {number} [modelValue=""] - The value of the input number.
 * @property {hasBorder} [hasBorder=true] - Whether the input has a border.
 * @property {string} [placeholder="Select option"] - The placeholder text for the input.
 * @property {string} [label] - The label for the input field.
 * @property {number} [max=100] - The maximum value allowed.
 * @property {icon} [icon=user'] - The icon for the input field.
 * @property {number} [min=0] - The minimum value allowed.
 * @property {boolean} [disabled=false] - Whether the input is disabled.
 * @property {string} [errorMessage] - The error message to display.
 */

/** @type {Props} */
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  hasBorder: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "Select option",
  },
  label: String,
  max: {
    type: Number,
    default: 100,
  },
  min: {
    type: Number,
    default: 0,
  },
  icon: String,
  disabled: Boolean,
  errorMessage: String,
});

const emit = defineEmits(["update:modelValue", "change"]);

const internalValue = ref(props.modelValue);
const error = ref("");

const validate = () => {
  if (Number(internalValue.value) < props.min) {
    error.value = `Value cannot be less than ${props.min}`;
  } else if (Number(internalValue.value) > props.max) {
    error.value = `Value cannot be greater than ${props.max}`;
  } else {
    error.value = "";
  }
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue === null || newValue === undefined) {
      internalValue.value = props.min;
    } else {
      internalValue.value = newValue;
    }
  },
  { immediate: true }
);

const increase = () => {
  const value = Number(internalValue.value) + 1;

  if (value <= props.max) {
    internalValue.value = value;
    emit("update:modelValue", internalValue.value);
    emit("change", internalValue.value);
  }
  validate();
};

const decrease = () => {
  const value = Number(internalValue.value) - 1;
  if (value >= props.min) {
    internalValue.value = value;
    emit("update:modelValue", internalValue.value);
    emit("change", internalValue.value);
  }
  validate();
};
</script>

<style lang="scss" scoped>
.ff_input_number {
  &.has-border {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border: 2px solid #efefef;
    border-radius: 7px;
    padding: 17px;
    min-height: 40px;
    gap: 8px;
    cursor: pointer;
    transition: 0.2s ease-in;
    position: relative;
    width: auto;
    max-width: inherit;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    flex: 1 1 auto;
  }
  .input_error {
    display: flex;
    margin-top: 10px;
    p {
      margin: 0 !important;
      font-size: 14px;
      text-align: left !important;
      font-weight: 500;

      &.error {
        color: var(--color--error);
      }
      &.hint {
        color: var(--color--text);
      }
    }
  }
  label {
    font-family: var(--font-2);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    color: #000;
    display: inline-flex;
    line-height: initial;
    margin-bottom: 13px;
  }
  .ff_input_number_container {
    display: flex;
    align-items: center;
    &.disabled {
      .ff-icon {
        cursor: not-allowed;
        opacity: 0.5;
        pointer-events: none;
      }
      .ff_input_number_input {
        opacity: 0.5;
      }
    }
    .ff-icon {
      border: 1px solid #e8e8e8;
      border-radius: 50px;
      padding: 5px;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.2s ease;
      box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
      &:hover {
        background-color: #f5f5f5;
        transform: scale(1.1);
      }
      .icon {
        color: red;
        fill: red;
        stroke: red;
      }
    }
    .ff_input_number_input {
      min-width: 80px;
      display: flex;
      text-align: center;
      justify-content: center;
      font-weight: 600;
      font-size: 20px !important;
    }
  }
}
</style>
