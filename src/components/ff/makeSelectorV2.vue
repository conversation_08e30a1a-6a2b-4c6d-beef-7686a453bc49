<template>
  <div class="listing-list-container">
    <template v-if="optionsLoading">
      <div class="flex gap-2 mt-5 flex-wrap">Loading</div>
    </template>
    <template v-else-if="options.length > 0">
      <ff-input
        :label="$t('inputs.search_vehicle_make')"
        v-model="searchQuery"
        ref="RefInputVehicleEngine"
        id="email"
        placeholder="Bmw, Audi, Mercedes-Benz, etc."
        :disabled="false"
        :extra-class="'mb-2'"
      />

      <h6 class="mt-4 mb-3" v-if="filteredImportantMakes.length > 0">
        Famous car makes
      </h6>
      <div class="list">
        <div
          class="listing-list-item"
          v-for="(make, index) in filteredImportantMakes"
          :key="index"
          @click="select(make)"
          :class="{ active: value && value.id == make.id }"
        >
          <ff-icon
            icon="checkmark"
            v-if="value && value.id == make.id"
            class="checked_icon"
            color="inherit"
          ></ff-icon>
          <img :src="make.logo" />
          <p>{{ make.name }}</p>
        </div>
      </div>
      <h6 class="mt-4 mb-3" v-if="filteredOptions.length > 0">
        Other car makes
      </h6>
      <div class="list" v-if="filteredOptions.length > 0">
        <div
          class="listing-list-item"
          v-for="(make, index) in filteredOptions"
          :key="index"
          @click="select(make)"
          :class="{ active: value && value.id == make.id }"
        >
          <ff-icon
            icon="checkmark"
            v-if="value && value.id == make.id"
            class="checked_icon"
            color="inherit"
          ></ff-icon>
          <img :src="make.logo" />
          <p>{{ make.name }}</p>
        </div>
      </div>
    </template>
  </div>

  <div class="input_errors" v-if="errorMessage">
    <p class="error">{{ $t("validations." + errorMessage) }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElaDropdownClasses } from "@/utilities/PrimeVueClasses";
const componentClasses = ref({ ...ElaDropdownClasses });

const type = ref("list");
const optionsLoading = ref(true);
const options = ref([]);
const important_makes = ref([]);
const searchQuery = ref("");
const { get_vehicle_makes } = useVehicles();

const props = defineProps({
  value: Object,
  errorMessage: String,
});
const emits = defineEmits(["update:value", "change"]);

onMounted(() => {
  load_options();
});

function updateModelValue(newValue: any) {
  emits("update:value", newValue.value);
}

const load_options = async () => {
  optionsLoading.value = true; // Start loading

  try {
    let response = await get_vehicle_makes({});
    if (response.data.success) {
      const importantMakeNames = new Set([
        "Audi",
        "BMW",
        "Mercedes-Benz",
        "Volkswagen",
        "Toyota",
        "Honda",
        "Ford",
        "Chevrolet",
        "Hyundai",
        "Nissan",
      ]);

      const allMakes = response.data.data;

      important_makes.value = allMakes.filter((make) =>
        importantMakeNames.has(make.name)
      );
      options.value = allMakes.filter(
        (make) => !importantMakeNames.has(make.name)
      );
    }
  } catch (err) {
    // notify(handleError(err), "error", 5000);
  } finally {
    optionsLoading.value = false; // End loading
  }
};

// Computed properties to filter the car makes based on the search query
const filteredImportantMakes = computed(() => {
  return important_makes.value.filter((make) =>
    make.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const filteredOptions = computed(() => {
  return options.value.filter((make) =>
    make.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const select = async (make) => {
  emits("change", { id: make.id, make_object: make });
};
</script>

<style lang="scss"></style>
