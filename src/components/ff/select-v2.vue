<template>
  <template v-if="listType == 'select'">
    <ElaDropdown
      :pt="componentClasses"
      :model-value="internalValue"
      @change="updateModelValue"
      :loading="optionsLoading"
      :options="options"
      :disabled="disabled"
      optionLabel="name"
      @before-show="require_options_from_parent"
      :placeholder="placeholder"
    >
      <template #content="slotProps">
        {{ slotProps }}
      </template>
      <template #value="slotProps">
        <div
          v-if="slotProps.value"
          class="ElaDropdownRow is_selected"
          :class="{ 'bigger-images': OptionImageBigger }"
        >
          <template v-if="OptionImageSelector">
            <img
              :src="
                OptionImageSelectorPath +
                slotProps.value[labelSelector] +
                OptionImageSelectorPathExtension
              "
            />
            <template v-if="translateOptions">
              <div>
                {{ $t(translateOptions + slotProps.value[labelSelector]) }}
              </div>
            </template>
            <template v-else>
              <div>{{ slotProps.value[labelSelector] }}</div>
            </template>
          </template>
          <template v-else>
            <template v-if="translateOptions">
              <div>
                {{ $t(translateOptions + slotProps.value[labelSelector]) }}
              </div>
            </template>
            <template v-else>
              <div>{{ slotProps.value[labelSelector] }}</div>
            </template>
          </template>
        </div>
        <span v-else>
          {{ slotProps.placeholder }}
        </span>
      </template>
      <template #option="slotProps">
        <div
          class="ElaDropdownRow"
          :class="{ 'bigger-images': OptionImageBigger }"
        >
          <template v-if="OptionImageSelector">
            <img
              :src="
                OptionImageSelectorPath +
                slotProps.option[labelSelector] +
                OptionImageSelectorPathExtension
              "
            />
            <template v-if="translateOptions">
              <div>
                {{ $t(translateOptions + slotProps.option[labelSelector]) }}
              </div>
            </template>
            <template v-else>
              <div>
                {{ slotProps.option[labelSelector] }}
              </div>
            </template>
          </template>
          <template v-else>
            <template v-if="translateOptions">
              <div>
                {{ $t(translateOptions + slotProps.option[labelSelector]) }}
              </div>
            </template>
            <template v-else>
              <div>{{ slotProps.option[labelSelector] }}</div>
            </template>
          </template>
        </div>
      </template>
    </ElaDropdown>
  </template>
  <template v-else>
    <template v-if="optionsLoading">
      <div class="ff-skeleton-loader">Loading</div>
    </template>
    <template v-else>
      <div
        class="listing-list-container"
        :class="{ 'bigger-images': OptionImageBigger }"
      >
        <div class="list square">
          <div
            class="listing-list-item"
            v-for="(listItem, index) in options"
            :key="index"
            @click="selectValueFromList(listItem)"
            :class="{
              active:
                internalValue &&
                internalValue[idSelector] == listItem[idSelector],
            }"
          >
            <ff-icon
              icon="checkmark"
              v-if="
                internalValue &&
                internalValue[idSelector] == listItem[idSelector]
              "
              class="checked_icon"
              color="inherit"
            ></ff-icon>
            <img
              :src="
                OptionImageSelectorPath +
                listItem[labelSelector] +
                OptionImageSelectorPathExtension
              "
            />
            <template v-if="translateOptions">
              <p>{{ $t(translateOptions + listItem[labelSelector]) }}</p>
            </template>
            <template v-else>
              <p>{{ listItem[labelSelector] }}</p>
            </template>
          </div>
        </div>
      </div>
    </template>
  </template>

  <div class="input_errors" v-if="errorMessage">
    <p class="error">{{ $t("validations." + errorMessage) }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElaDropdownClasses } from "@/utilities/PrimeVueClasses";
const componentClasses = ref(ElaDropdownClasses);

const props = defineProps({
  modelValue: Object,
  options: Array,
  optionsLoading: Boolean,
  errorMessage: String,
  disabled: Boolean,
  placeholder: String,
  OptionImageSelector: String,
  OptionImageSelectorPath: String,
  OptionImageSelectorPathExtension: String,
  OptionImageBigger: Boolean,
  translateOptions: {
    type: String,
    required: false,
    default: "",
  },
  listType: {
    type: String,
    required: false,
    default: "select",
  },
  labelSelector: {
    type: String,
    required: false,
    default: "label",
  },
  idSelector: {
    type: String,
    required: false,
    default: "id",
  },
});

const { modelValue } = props;
const internalValue = ref(modelValue);
const emits = defineEmits(["update:value", "loadOptions"]);

watch(
  () => props.modelValue,
  (newValue) => {
    internalValue.value = newValue;
  }
);

function updateModelValue(newValue: any) {
  emits("update:value", newValue);
}

const require_options_from_parent = () => {
  emits("loadOptions");
};
const selectValueFromList = async (e) => {
  internalValue.value = e;
  let modifier = {
    value: e,
  };
  emits("update:value", modifier);
};
</script>

<style lang="scss" scoped>
.ff-skeleton-loader {
  display: flex;
  gap: 20px;
  width: 100%;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding-top: 20px;

  .ElaSkeleton {
    width: 20% !important;
    height: 100px !important;
  }
}

.p-dropdown .p-dropdown-label:focus {
  outline: none;
  border: none;
}
</style>
