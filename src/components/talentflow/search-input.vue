<template>
  <div
    :class="[
      'ff_input relative',
      extraClass,
      { 'has-error': hasError, disabled: disabled },
    ]"
  >
    <label :for="id" v-if="label">{{ label }}</label>

    <div
      class="ff-input-container"
      :class="{
        'input-with-left-icon': leftIcon,
        'input-with-left-leading-text': leftLeadingText,
        'input-with-right-leading-text': rightLeadingText,
        'input-with-right-icon': rightIcon || isLoading,
        'ff-input-with-icon': leftIcon || rightIcon || isLoading,
      }"
    >
      <span
        v-if="leftIcon && !leftLeadingText"
        class="icon_item left-icon"
        @click="onIconClick('left')"
      >
        <component
          v-if="iconMap[leftIcon]"
          :is="iconMap[leftIcon]"
          v-bind="defaultIconProps"
        />
      </span>
      <span v-if="leftLeadingText" class="left-leading-text">
        {{ leftLeadingText }}
      </span>

      <input
        :autocomplete="autocomplete"
        :type="inputType || type"
        v-bind="
          maxNumber !== undefined
            ? { max: maxNumber, 'v-limit-length': maxNumber }
            : {}
        "
        ref="inputElement"
        :id="id"
        :placeholder="placeholder"
        :value="inputValue"
        @input="updateValue"
        @focus="onFocus"
        @blur="emitBlur"
        @keydown="onKeyDown"
        @keydown.tab="onTabPressed"
        @keydown.enter="onEnterPressed"
        :disabled="disabled"
      />

      <span
        v-if="rightIcon || isLoading"
        class="icon_item right-icon"
        @click="onIconClick('right')"
        :class="{ cursor: rightIconClickable }"
      >
        <Loader v-if="isLoading" size="lg" />
        <component
          v-else-if="rightIcon && iconMap[rightIcon]"
          :is="iconMap[rightIcon]"
          v-bind="defaultIconProps"
        />
      </span>
      <span v-if="rightLeadingText" class="right-leading-text">
        {{ rightLeadingText }}
      </span>
    </div>

    <!-- Suggestions Dropdown -->
    <div
      v-if="showSuggestions && suggestions.length > 0"
      class="suggestions-dropdown"
      ref="suggestionsDropdownRef"
    >
      <div
        v-for="(suggestion, index) in suggestions"
        :key="index"
        class="suggestion-item"
        :class="{ selected: index === selectedIndex }"
        @click="onSuggestionClick(suggestion)"
      >
        <template v-if="searchType == 'country'">
          {{ suggestion.formattedAddress }}
        </template>
        <template v-else>
          {{ suggestion.name || suggestion.label || suggestion }}
        </template>
      </div>
    </div>

    <div class="input_errors" v-if="displayError">
      <p class="error">{{ displayError }}</p>
    </div>
    <template v-else-if="hint">
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
import { ref, watch, computed, inject, nextTick } from "vue";
import { iconMap, type IconName } from "@55/iconMap";
import Loader from "@55/loader.vue";
import { useField } from "vee-validate";

import { AppService } from "@/services/app.service";
import type { Job, JobResponse } from "@/types/job.types";
import type { Place, PlaceResponse } from "@/types/place.types";
import type { ApiResponse } from "@/types/general.types";

// import { email } from '@/assets/icons/email'
const inputElement = ref<HTMLElement | null>(null);
const suggestionsDropdownRef = ref<HTMLElement | null>(null);
const props = defineProps({
  modelValue: String,
  value: String,
  hasRightLabel: Boolean,
  rightLabelText: String,
  rightLabelDirection: String,
  leftIcon: {
    type: String as PropType<IconName>,
    required: false,
  },
  leftLeadingText: String,
  rightLeadingText: String,
  max: {
    type: [String, Number],
    required: false,
  },
  rightIcon: {
    type: String as PropType<IconName>,
    required: false,
  },

  rightIconClickable: {
    type: Boolean,
    default: false,
  },
  label: String,
  type: {
    type: String,
    default: "text",
  },
  hint: String,
  id: String,
  placeholder: String,
  disabled: Boolean,
  autocomplete: String,
  extraClass: String,
  errorMessage: String,

  // Input type for different data types
  inputType: {
    type: String as PropType<
      "text" | "tel" | "email" | "number" | "password" | "url"
    >,
    default: "text",
  },
  // Form integration props
  name: {
    type: String,
    required: false,
  },
  searchType: {
    type: String as PropType<"places" | "dreamRoles">,
    default: "country",
  },
});

function focus() {
  inputElement.value?.focus();
}

function clearValidationError() {
  internalError.value = "";
}

// Inject form context if available
const formContext = inject("formContext", null);

const minCharacters = ref(3);
// Check if we should use vee-validate (when inside BForm and name is provided)
const shouldUseVeeValidate = formContext && props.name;

// Setup vee-validate field if inside a form
const veeField = shouldUseVeeValidate
  ? useField(props.name, undefined, {
      syncVModel: true,
      initialValue: props.modelValue || "",
    })
  : null;

// Use vee-validate value and error if available, otherwise use internal state
const inputValue = ref(props.modelValue || '');

const internalError = ref<string>("");

const onFocus = () => {};

const suggestions = ref<any[]>([]);
const isLoading = ref(false);
const showSuggestions = ref(false);
const selectedIndex = ref(-1);

// Custom debounce function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}

const debouncedSearch = debounce(async (query: string) => {
  if (query.length < minCharacters.value) {
    suggestions.value = [];
    return;
  }

  try {
    isLoading.value = true;
    let response:
      | ApiResponse<JobResponse | PlaceResponse | Job | Place>
      | undefined;
    if (props.searchType === "dreamRoles") {
      response = await AppService.searchJobPosition({
        q: query,
        type: "dreamRoles",
      });
    } else if (props.searchType === "places") {
      response = await AppService.searchPlace({ q: query, type: "places" });
    }

    if (response?.data) {
      if ("results" in response.data) {
        suggestions.value = response.data.results;
      } else {
        suggestions.value = [response.data];
      }
    }
  } catch (error) {
    console.error("Search error:", error);
    suggestions.value = [];
  } finally {
    isLoading.value = false;
  }
}, 300);

const updateValue = (event: Event) => {
  const newValue = (event.target as HTMLInputElement).value;
  inputValue.value = newValue;

  if (internalError.value && newValue.length > 0) {
    internalError.value = "";
  }

  if (props.searchType) {
    debouncedSearch(newValue);
    showSuggestions.value = true;
  }

  // Emit change to trigger validation when input is cleared
  if (shouldUseVeeValidate && veeField) {
    veeField.value.value = ''; // Clear the validation value
    emit("change", ''); // Emit change to trigger validation
  }
};

const onSuggestionClick = (suggestion: any) => {
const value = suggestion.name || suggestion.label || suggestion;
  inputValue.value = value;
  
  // Only update vee-validate field when a selection is made
  if (shouldUseVeeValidate && veeField) {
    veeField.value.value = value;
  }
  
  suggestions.value = [];
  showSuggestions.value = false;
  emit("update:modelValue", value);
  emit("change", suggestion); // Emit the entire suggestion object
};

const onKeyDown = (event: KeyboardEvent) => {
  console.log('Key down:', event.key, 'showSuggestions:', showSuggestions.value, 'suggestions.length:', suggestions.value.length);
  if (!showSuggestions.value || suggestions.value.length === 0) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      selectedIndex.value = Math.min(selectedIndex.value + 1, suggestions.value.length - 1);
      nextTick(scrollToSelected);
      break;
    case 'ArrowUp':
      event.preventDefault();
      selectedIndex.value = Math.max(selectedIndex.value - 1, 0);
      nextTick(scrollToSelected);
      break;
    case 'Enter':
      event.preventDefault();
      if (selectedIndex.value >= 0) {
        onSuggestionClick(suggestions.value[selectedIndex.value]);
      }
      break;
    case 'Escape':
      event.preventDefault();
      showSuggestions.value = false;
      selectedIndex.value = -1;
      break;
  }
};

const scrollToSelected = () => {
  console.log('Scrolling to selected. Index:', selectedIndex.value);
  const dropdownEl = suggestionsDropdownRef.value;
  if (dropdownEl) {
    const selectedElement = dropdownEl.querySelector('.suggestion-item.selected');
    if (selectedElement) {
      console.log('Selected element found:', selectedElement);
      selectedElement.scrollIntoView({ block: 'nearest' });
    } else {
      console.log('Selected element NOT found.');
    }
  } else {
    console.log('Dropdown element NOT found.');
  }
};

// Reset selected index when suggestions change
watch(suggestions, () => {
  selectedIndex.value = -1;
});

const hasError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const displayError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const emit = defineEmits([
  "update:modelValue",
  "iconClick",
  "change",
  "blur",
  "tabPressed",
  "enterPressed",
  "rightLabelClicked",
  "select",
]);

const rightLabelClicked = () => {
  emit("rightLabelClicked");
};

const onIconClick = (position: "left" | "right") => {
  emit("iconClick", position);
};
const emitBlur = () => {
  emit("blur");
};
const onTabPressed = (event: any) => {
  emit("tabPressed", event);
};
const onEnterPressed = (event: any) => {
  emit("enterPressed", event);
};

// Watch for model value changes to sync input value
watch(() => props.modelValue, (newValue) => {
  if (newValue !== inputValue.value) {
    inputValue.value = newValue || '';
  }
});

const maxNumber = computed(() => {
  const n = Number(props.max);
  return isNaN(n) ? undefined : n;
});

const defaultIconProps = {
  size: 20,
};

defineExpose({ focus, clearValidationError });
</script>

<style lang="scss" scoped>

</style>
