<template>
  <div
    :class="[
      'ff_input',
      extraClass,
      { 'has-error': hasError, disabled: disabled },
    ]"
  >
    <label :for="id" v-if="label">{{ label }}</label>

    <div class="ff-input-container">
      <input
        :autocomplete="autocomplete"
        :type="type"
        ref="inputElement"
        :id="id"
        :placeholder="placeholder"
        :value="internalValue"
        @input="updateValue"
        @focus="onFocus"
        @blur="emitBlur"
        @keydown="onKeyDown"
        @keydown.tab="onTabPressed"
        @keydown.enter="onEnterPressed"
        :disabled="disabled"
      />
    </div>

    <!-- Suggestions Dropdown -->

    <div class="input_errors" v-if="displayError">
      <p class="error">{{ displayError }}</p>
    </div>
    <template v-else-if="hint">
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
import { ref, watch, computed, inject } from "vue";
import { iconMap, type IconName } from "@/components/ff/iconMap";

import { useField } from "vee-validate";
import { z } from "zod";

// import { email } from '@/assets/icons/email'
const inputElement = ref<HTMLElement | null>(null);
const props = defineProps({
  modelValue: String,
  value: String,
  label: String,
  type: {
    type: String as PropType<
      "text" | "tel" | "email" | "number" | "password" | "url"
    >,
    default: "text",
  },
  hint: String,
  id: String,
  placeholder: String,
  disabled: Boolean,
  autocomplete: String,
  extraClass: String,
  errorMessage: String,

  // Validation props
  validationType: {
    type: String as PropType<
      "email" | "password" | "phone" | "required" | "none"
    >,
    default: "none",
  },
  validationMessage: String,

  // Form integration props
  name: {
    type: String,
    required: false,
  },
});

function focus() {
  inputElement.value?.focus();
}

function clearValidationError() {
  internalError.value = "";
}

// Inject form context if available
const formContext = inject("formContext", null);

// Check if we should use vee-validate (when inside BForm and name is provided)
const shouldUseVeeValidate = formContext && props.name;

// Setup vee-validate field if inside a form
const veeField = shouldUseVeeValidate
  ? useField(props.name, undefined, {
      syncVModel: true,
      initialValue: props.modelValue || "",
    })
  : null;

// Use vee-validate value and error if available, otherwise use internal state
const internalValueRef = shouldUseVeeValidate ? null : ref(props.modelValue);

const internalError = ref<string>("");

// Computed property for the current value
const internalValue = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return veeField.value.value;
  }
  return internalValueRef?.value;
});

const onFocus = () => {};

// Handle Tab key to accept inline suggestion
const onKeyDown = (event: KeyboardEvent) => {
  // if (
  //   event.key === "Tab" &&
  //   inlineSuggestion.value &&
  //   props.showInlineSuggestion
  // ) {
  //   event.preventDefault();
  //   emit("update:modelValue", newValue);
  //   emit("change", newValue);
  // }
};

const hasError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const displayError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const emit = defineEmits([
  "update:modelValue",
  "change",
  "blur",
  "tabPressed",
  "enterPressed",
]);

const updateValue = (event: Event) => {
  const newValue = (event.target as HTMLInputElement).value;

  if (shouldUseVeeValidate && veeField) {
    // If using vee-validate, update the field value
    veeField.value.value = newValue;
  } else {
    // Traditional approach
    if (internalValueRef) {
      internalValueRef.value = newValue;
    }

    // Clear error when user starts typing (if there's an error)
    if (internalError.value && newValue.length > 0) {
      internalError.value = "";
    }
  }

  emit("update:modelValue", newValue);
  emit("change", newValue);
};

const rightLabelClicked = () => {
  emit("rightLabelClicked");
};

const emitBlur = () => {
  emit("blur");
};
const onTabPressed = (event: any) => {
  emit("tabPressed", event);
};
const onEnterPressed = (event: any) => {
  emit("enterPressed", event);
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (shouldUseVeeValidate && veeField) {
      veeField.value.value = newValue || "";
    } else {
      if (internalValueRef) {
        internalValueRef.value = newValue;
      }
    }
  }
);

defineExpose({ focus, clearValidationError });
</script>

<style scoped lang="scss"></style>
