<template>
  <div class="salary-range-selector">
    <label v-if="label" class="form-label">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>

    <div class="slider-container" ref="sliderRef">
      <div class="slider-track"></div>
      <div class="slider-fill" :style="fillStyle"></div>
      <div
        class="slider-thumb from-thumb"
        :class="{ active: fromValue === activePointIndexFrom }"
        :style="{ left: fromThumbPosition + '%' }"
        @mousedown="startDrag($event, 'from')"
        @touchstart="startDrag($event, 'from')"
      ></div>
      <div
        class="slider-thumb to-thumb"
        :class="{ active: toValue === activePointIndexTo }"
        :style="{ left: toThumbPosition + '%' }"
        @mousedown="startDrag($event, 'to')"
        @touchstart="startDrag($event, 'to')"
      ></div>
    </div>

    <div class="slider-labels">
      <span
        v-for="(point, index) in sliderPoints"
        :key="index"
        class="label-item"
        :class="{ active: index >= fromValue && index <= toValue }"
        :style="{ left: (index / (sliderPoints.length - 1)) * 100 + '%' }"
      >
        {{ point.label }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";

interface Props {
  modelValue?: string; // Expects "FROM-TO"
  name?: string;
  label?: string;
  required?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  name: "",
  label: "",
  required: false,
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
  "update:fromRangeSalary": [value: number];
  "update:toRangeSalary": [value: number];
}>();

const sliderRef = ref<HTMLElement | null>(null);
const isDraggingFrom = ref(false);
const isDraggingTo = ref(false);

const fromValue = ref(0);
const toValue = ref(1);

const sliderPoints = [
  { label: "$0", value: 0 },
  { label: "$50K", value: 50000 },
  { label: "$100K", value: 100000 },
  { label: "$150K", value: 150000 },
  { label: "$200K", value: 200000 },
  { label: "$250K+", value: 250000 },
];


const fromThumbPosition = computed(() => fromValue.value / (sliderPoints.length - 1) * 100);
const toThumbPosition = computed(() => toValue.value / (sliderPoints.length - 1) * 100);

const fillStyle = computed(() => {
  const leftPercent = fromThumbPosition.value;
  const rightPercent = toThumbPosition.value;
  return {
    left: `${leftPercent}%`,
    width: `${rightPercent - leftPercent}%`,
  };
});

// Determine which label should be active based on current thumb positions
const activePointIndexFrom = computed(() => fromValue.value);
const activePointIndexTo = computed(() => toValue.value);

const updateSliderValues = (clientX: number, type: 'from' | 'to') => {
  if (!sliderRef.value) return;

  const sliderRect = sliderRef.value.getBoundingClientRect();
  const clickX = clientX - sliderRect.left;
  const percentage = Math.max(0, Math.min(1, clickX / sliderRect.width));

  const totalSteps = sliderPoints.length - 1;
  const nearestStep = Math.round(percentage * totalSteps);

  if (type === 'from') {
    fromValue.value = Math.min(nearestStep, toValue.value - 1);
  } else {
    toValue.value = Math.max(nearestStep, fromValue.value + 1);
  }

  emitCurrentValue();
};

const startDrag = (event: MouseEvent | TouchEvent, type: 'from' | 'to') => {
  event.preventDefault();
  if (type === 'from') {
    isDraggingFrom.value = true;
  } else {
    isDraggingTo.value = true;
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDraggingFrom.value) {
      updateSliderValues(e.clientX, 'from');
    } else if (isDraggingTo.value) {
      updateSliderValues(e.clientX, 'to');
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (e.touches.length === 1) {
      if (isDraggingFrom.value) {
        updateSliderValues(e.touches[0].clientX, 'from');
      } else if (isDraggingTo.value) {
        updateSliderValues(e.touches[0].clientX, 'to');
      }
    }
  };

  const stopDrag = () => {
    isDraggingFrom.value = false;
    isDraggingTo.value = false;
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('mouseup', stopDrag);
    window.removeEventListener('touchmove', handleTouchMove);
    window.removeEventListener('touchend', stopDrag);
  };

  window.addEventListener('mousemove', handleMouseMove);
  window.addEventListener('mouseup', stopDrag);
  window.addEventListener('touchmove', handleTouchMove);
  window.addEventListener('touchend', stopDrag);
};

const emitCurrentValue = () => {
  const fromSalary = sliderPoints[fromValue.value].value;
  let toSalary: number;

  if (toValue.value === sliderPoints.length - 1) {
    toSalary = sliderPoints[toValue.value].value; // Keep as number, the + will be handled in display
  } else {
    toSalary = sliderPoints[toValue.value].value;
  }

  emit("update:modelValue", `${fromSalary}-${toSalary}`);
  emit("update:fromRangeSalary", fromSalary);
  emit("update:toRangeSalary", toSalary);
};

watch([fromValue, toValue], () => {
  emitCurrentValue();
}, { immediate: true });

// Initialize from modelValue prop
watch(() => props.modelValue,
  (newVal) => {
    if (newVal) {
      const parts = newVal.split('-');
      if (parts.length === 2) {
        const fromStr = parts[0];
        const toStr = parts[1];

        const fromNum = parseFloat(fromStr);
        
        const isToPlus = toStr.endsWith('+');
        const toNum = parseFloat(toStr);

        const fromIndex = sliderPoints.findIndex(p => p.value === fromNum);
        let toIndex = -1;

        if (isToPlus) {
          toIndex = sliderPoints.findIndex(p => p.value === toNum && p.label.endsWith('+'));
        } else {
          toIndex = sliderPoints.findIndex(p => p.value === toNum);
        }

        if (fromIndex !== -1) fromValue.value = fromIndex;
        if (toIndex !== -1) toValue.value = toIndex;
      }
    }
  }, { immediate: true });

</script>

<style scoped lang="scss">
.salary-range-selector {
  padding: 1.5rem 0;
  background: #ffffff;
  border-radius: 12px;
  
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align:left;
  .form-label {
    font-family: var(--font-2);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    color: #000;
    line-height: 24px;
    display: inline-flex;
    margin-bottom: 0.5rem;
  }

  .required {
    color: #247447;
  }

  .slider-container {
    position: relative;
    width: 100%;
    height: 16px;
    border-radius: 8px;
    background: #e0e0e0;
    margin-top: 1rem;
    display: flex;
    align-items: center;
  }

  .slider-track {
    position: absolute;
    left: 0;
    right: 0;
    height: 100%;
    border-radius: 8px;
    background: #f0f0f0;
  }

  .slider-fill {
    position: absolute;
    height: 100%;
    border-radius: 8px;
    background: #247447;
    /* width and left are handled by fillStyle computed property */
  }

  .slider-thumb {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ffffff;
    border: 2px solid #4F67D6;
    cursor: grab;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;

    &.active {
      border: 2px solid #247447;
      box-shadow: 0 0 0 4px rgba(36, 116, 71, 0.20)
    }
  }

  .slider-labels {
    display: flex;
    justify-content: space-between;
    width: 90%;
    margin-top: 0.75rem;
    font-family: var(--font-2);
    font-size: 14px;
    font-weight: 500;
    color: #929292;
    position: relative;
    .label-item {
      text-align: center;
      position: absolute;
      transform: translateX(0);

      &.active {
        color: #247447;
        font-weight: 600;
      }
    }
  }
}
</style>
