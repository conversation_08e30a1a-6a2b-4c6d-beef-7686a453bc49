<template>
  <div class="form-header">
    <h1 class="form-title">Let's Talk About Your Dream Role</h1>
    <p class="form-subtitle">
      We want to match you with roles that truly excite you, tell us what you're
      looking for.
    </p>
  </div>

  <BForm
    ref="formRef"
    :validation-schema="jobPreferencesSchema"
    :initial-values="{
      dreamJob: formData.dreamJob,
      preferedLocation: formData.preferedLocation,
      experience: formData.experience,
      workSetup: formData.workSetup,
      salary: formData.salary,
    }"
    @validation-change="handleValidationChange"
    class="onboarding-form"
  >
    <!-- Dream Job Input -->
    <div class="form-group">
      <SearchInput
        :left-icon="'job'"
        label="What's your dream job?"
        name="dreamJob"
        :model-value="formData.dreamJob"
        @update:model-value="handleDreamJobChange"
        searchType="jobPosition"
        placeholder="e.g. <PERSON><PERSON> Developer, Marketing Manager, UX Designer"
        :required="true"
        help-text="Be as specific or broad as you'd like — we'll refine it later."
        :show-suggestions-on-focus="true"
      />
    </div>

    <!-- Location Input -->
    <div class="form-group">
      <SearchInput
        :left-icon="'mapPin'"
        label="Where would you love to work?"
        name="preferedLocation.formattedAddress"
        :model-value="formData.preferedLocation.formattedAddress"
        @update:model-value="handlePreferedLocationChange"
        placeholder="e.g. United States"
        :required="true"
        :min-characters="2"
      />
    </div>

    <!-- Desired Salary -->
    <div class="form-group">
      <SalaryRangeSelector
        label="What's your desired salary?"
        name="salary"
        :model-value="formData.salary"
        @update:model-value="handleSalaryChange"
        :required="true"
      />
    </div>

    <!-- Years of Experience -->
    <div class="form-group">
      <ExperienceSelector
        label="Years of experience"
        name="experience"
        :model-value="formData.experience"
        @update:model-value="handleExperienceChange"
      />
    </div>

    <!-- Work Setup -->
    <WorkSetupSelect
      label="What's your ideal work setup?"
      name="workSetup"
      :model-value="formData.workSetup"
      @update:model-value="handleWorkSetupChange"
    />
  </BForm>

  <!-- Form Actions -->
  <div class="form-actions">
    <B_Button
      type="primary"
      size="lg"
      :is-box="false"
      :disabled="!isFormValid"
      :is-loading="isLoading"
      @click="handleSubmit"
      class="submit-btn"
    >
      Let's Get Your Resume Ready
    </B_Button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import SearchInput from "@/components/talentflow/search-input.vue";
import WorkSetupSelect from "@/components/talentflow/work-setup-select.vue";
import SalaryRangeSelector from "@/components/talentflow/salary-range-selector.vue";
import B_Button from "@/components/talentflow/button.vue";
import BForm from "@/components/ff/form.vue";
import { useRegistrationStore } from "@/stores/registration.store";
import { jobPreferencesSchema } from "@/utils/validation-schemas/onboarding/jobPreferencesSchema";
import ExperienceSelector from "@/components/talentflow/experience-selector.vue";
import { useRouter } from "vue-router";
import { AuthService } from "@/services";

const router = useRouter();

const registrationStore = useRegistrationStore();
const emit = defineEmits<{ validationChange: [isValid: boolean] }>();
const isLoading = ref(false);

// The BForm's validation status is now the single source of truth for this step's validity.
const isFormValid = ref(false);

const formRef = ref();

const handleValidationChange = (isValid: boolean) => {
  isFormValid.value = isValid;
  emit("validationChange", isValid);
};

// These handlers are still needed to update the store,
// but they no longer need to call checkOverallValidity().
const handleDreamJobChange = (value: string) => {
  formData.value.dreamJob = value;
  // The validation will be handled by vee-validate through the BForm
};

const handlePreferedLocationChange = (value: string) => {
  formData.value.preferedLocation.formattedAddress = value;
};

const handleSalaryChange = (value: string) => {
  formData.value.salary = value;
};

const handleExperienceChange = (value: number) => {
  formData.value.experience = value;
};

const handleWorkSetupChange = (value: string) => {
  formData.value.workSetup = value;
};

// Handle form submission and navigation
const handleSubmit = async () => {
  if (!formRef.value) return;

  // Trigger validation on all fields
  const isValid = await formRef.value.validate();

  if (isValid.valid) {
    // Ensure all current form values are saved to store before navigation
    console.log("📋 Submitting job preferences:", formData.value);

    registrationStore.updateJobPreferences("dreamJob", formData.value.dreamJob);
    registrationStore.updateJobPreferences(
      "preferedLocation",
      formData.value.preferedLocation
    );
    registrationStore.updateJobPreferences(
      "workSetup",
      formData.value.workSetup
    );
    registrationStore.updateJobPreferences("salary", formData.value.salary);
    registrationStore.updateJobPreferences(
      "experience",
      formData.value.experience
    );

    isLoading.value = true;

    // TODO: Add API call here similar to personal-info-step
    // let result = await AuthService.update_onboarding_step_2(mappedData);
    // if (result.success) {
    // Log store state after update
    console.log(
      "🏪 Store job preferences after update:",
      registrationStore.jobPreferences
    );

    // Navigate to next step
    router.push({ name: "resume" });
    // }
    isLoading.value = false;
  } else {
    console.warn("⚠️ Form validation failed, cannot submit");
  }
};

const formData = ref({
  dreamJob: registrationStore.jobPreferences.dreamJob || "",
  preferedLocation: {
    formattedAddress:
      registrationStore.jobPreferences.preferedLocation?.formattedAddress || "",
  },
  workSetup: registrationStore.jobPreferences.workSetup || "",
  salary: registrationStore.jobPreferences.salary || "",
  experience: registrationStore.jobPreferences.experience || 0,
});

defineExpose({
  isValid: isFormValid, // Expose the raw ref
});
</script>

<style scoped lang="scss">
@use "../../../assets/scss/mixin" as *;
</style>
