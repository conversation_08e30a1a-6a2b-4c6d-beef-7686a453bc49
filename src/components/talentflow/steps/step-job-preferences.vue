<template>
 
    <div class="form-header">
      <h1 class="form-title">Let's Talk About Your Dream Role</h1>
      <p class="form-subtitle">
        We want to match you with roles that truly excite you, tell us what
        you're looking for.
      </p>
    </div>
   
    <BForm
      ref="formRef"
      :validation-schema="jobPreferencesSchema"
      :initial-values="{
        dreamJob: formData.dreamJob,
        preferedLocation: formData.preferedLocation,
        experience: formData.experience,
        workSetup: formData.workSetup,
        fromRangeSalary: formData.fromRangeSalary,
        toRangeSalary: formData.toRangeSalary,
      }"
       @validation-change="handleValidationChange"
       class="onboarding-form"
    >
      <!-- Dream Job Input -->
      <div class="form-group">
        <SearchInput
          searchType="dreamRoles"
          :left-icon="'job'"
          label="What's your dream job?"
          name="dreamJob"
          @change="handleDreamJobChange"
         
          placeholder="e.g. <PERSON><PERSON> Developer, Marketing Manager, UX Designer"
          :required="true"
          help-text="Be as specific or broad as you'd like — we'll refine it later."
        />

       

      </div>

      <!-- Location Input -->
      <div class="form-group">
       
         <SearchInput
          searchType="places"
          :left-icon="'mapPin'"
          label="Where would you love to work?"
          name="preferedLocation"
          @change="handlePreferedLocationChange"
          placeholder="e.g. United States"
          :required="true"
        />

      </div>

      <!-- Desired Salary -->
      <div class="form-group">
        <SalaryRangeSelector
          label="What's your desired salary?"
          name="salary"
          :model-value="`${formData.currency}:${formData.fromRangeSalary}-${formData.toRangeSalary}`"
          :required="true"
          @update:fromRangeSalary="handleFromRangeSalaryChange"
          @update:toRangeSalary="handleToRangeSalaryChange"
        />
      </div>

      <!-- Years of Experience -->
      <div class="form-group">
        <InputNumber
          label="Years of experience"
          name="experience"
          :model-value="formData.experience"
          @update:model-value="handleExperienceChange"
        />
      </div>

      <!-- Work Setup -->
      <WorkSetupSelect
        label="What's your ideal work setup?"
        name="workSetup"
        :model-value="formData.workSetup"
        @update:model-value="handleWorkSetupChange"
      />
    </BForm>

    <!-- Form Actions -->
    <div class="form-actions">
      
      <B_Button
        type="primary"
        size="lg"
        :is-box="false"
        :disabled="!isFormValid"
        :is-loading="isLoading"
        @click="handleSubmit"
        class="submit-btn"
      >
        Let's Get Your Resume Ready
      </B_Button>
    </div>

</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import SearchInput from "@/components/talentflow/search-input.vue";
import WorkSetupSelect from "@/components/talentflow/work-setup-select.vue";
import SalaryRangeSelector from "@/components/talentflow/salary-range-selector.vue";
import B_Button from "@/components/talentflow/button.vue";
import BForm from "@/components/ff/form.vue";
import { useRegistrationStore } from "@/stores/registration.store";
import { jobPreferencesSchema } from "@/utils/validation-schemas/onboarding/jobPreferencesSchema";
import InputNumber from "@/components/talentflow/input-number.vue";
import type { WorkSetup } from "@/stores/types/auth.types";
import type { OnboardingSecondStepData } from "@/types/general.types"
import { AuthService } from "@/services";
const registrationStore = useRegistrationStore();
const isLoading = ref(false)
// Initialize form data from store
const formData = ref<{
  dreamJob: string | { id: string; name: string; seniority?: string; industries?: string[]; skills?: string[]; snowID?: string };
  preferedLocation: {
    id: string;
    name: string;
    city: string;
    district: string;
    zipCode: string;
    placeId: string;
    formattedAddress: string;
    country: {
      code: string;
      name: string;
    };
    state?: {
      code: string;
      name: string;
    };
    location: {
      lat: number;
      lng: number;
    };
  };
  workSetup: WorkSetup,
  fromRangeSalary: string;
  toRangeSalary: string;
  currency: string;
  experience: number;
}>({
  dreamJob: registrationStore.jobPreferences.dreamJob || "",
  preferedLocation: {
    id: registrationStore.jobPreferences.preferedLocation?.id || "",
    name: registrationStore.jobPreferences.preferedLocation?.name || "",
    city: registrationStore.jobPreferences.preferedLocation?.city || "",
    district: registrationStore.jobPreferences.preferedLocation?.district || "",
    zipCode: registrationStore.jobPreferences.preferedLocation?.zipCode || "",
    placeId: registrationStore.jobPreferences.preferedLocation?.placeId || "",
    formattedAddress: registrationStore.jobPreferences.preferedLocation?.formattedAddress || "",
    country: registrationStore.jobPreferences.preferedLocation?.country || { code: "", name: "" },
    state: registrationStore.jobPreferences.preferedLocation?.state,
    location: registrationStore.jobPreferences.preferedLocation?.location || { lat: 0, lng: 0 }
  },
  workSetup: registrationStore.jobPreferences.workSetup || "ON_SITE",
  fromRangeSalary: registrationStore.jobPreferences.fromRangeSalary || "",
  toRangeSalary: registrationStore.jobPreferences.toRangeSalary || "",
  currency: registrationStore.jobPreferences.currency || "USD",
  experience: registrationStore.jobPreferences.experience || 0,
});

// Form validity state
const isFormValid = ref(false);
const formRef = ref();



const handleSubmit = async () => {
  if (!formRef.value) return;

  // Trigger validation on all fields
  const isValid = await formRef.value.validate();

  if (isValid.valid) {
    // Map the form data to the new structure
    const mappedData = {
      dreamRoleId: typeof formData.value.dreamJob === 'object' ? formData.value.dreamJob.snowID : '',
      currency: formData.value.currency,
      fromRangeSalary: Number(formData.value.fromRangeSalary),
      toRangeSalary: Number(formData.value.toRangeSalary),
      yearsOfExperience: formData.value.experience,
      workSetup: formData.value.workSetup.toUpperCase(),
      desiredSalaryFrequency: "MONTHLY", // Default value as it's not in the form
      placeId: formData.value.preferedLocation.placeId,
      name: formData.value.preferedLocation.name,
      countryCode: formData.value.preferedLocation.country.code,
      countryName: formData.value.preferedLocation.country.name,
      city: formData.value.preferedLocation.city,
      district: formData.value.preferedLocation.district,
      zipCode: formData.value.preferedLocation.zipCode,
      formattedAddress: formData.value.preferedLocation.formattedAddress,
      latitude: formData.value.preferedLocation.location.lat,
      longitude: formData.value.preferedLocation.location.lng,
      purpose: "PREFERRED", 
      stateCode: formData.value.preferedLocation.state?.code || '',
      stateName: formData.value.preferedLocation.state?.name || ''
    };

    // Update the store with the mapped data
    registrationStore.updateJobPreferences("dreamJob", mappedData.dreamRoleId);
    registrationStore.updateJobPreferences("currency", mappedData.currency);
    registrationStore.updateJobPreferences("fromRangeSalary", mappedData.fromRangeSalary.toString());
    registrationStore.updateJobPreferences("toRangeSalary", mappedData.toRangeSalary.toString());
    registrationStore.updateJobPreferences("experience", mappedData.yearsOfExperience);
    registrationStore.updateJobPreferences("workSetup", mappedData.workSetup);
    registrationStore.updateJobPreferences("preferedLocation", {
      placeId: mappedData.placeId,
      name: mappedData.name,
      country: {
        code: mappedData.countryCode,
        name: mappedData.countryName
      },
      city: mappedData.city,
      district: mappedData.district,
      zipCode: mappedData.zipCode,
      formattedAddress: mappedData.formattedAddress,
      location: {
        lat: mappedData.latitude,
        lng: mappedData.longitude
      },
      state: mappedData.stateCode ? {
        code: mappedData.stateCode,
        name: mappedData.stateName
      } : undefined
    }); 

    isLoading.value = true; 
    if (!mappedData.dreamRoleId) {
      console.warn("⚠️ Dream role ID is required");
      isLoading.value = false;
      return;
    }
    let result = await AuthService.update_onboarding_step_2(mappedData as OnboardingSecondStepData);
    if (result.success) {
      // Log store state after update
      console.log(
        "🏪 Store personal info after update:",
        registrationStore.personalInfo
      );

      // Navigate to next step
      registrationStore.goToNextStep();
    }
     isLoading.value = false 
  } else {
    console.warn("⚠️ Form validation failed, cannot submit");
  }
};

// Event emitters
const emit = defineEmits<{
  validationChange: [isValid: boolean];
}>();

// Dream job method (simple - SearchInput handles the suggestions)
const handleDreamJobChange = (value: string | { id: string; name: string; seniority?: string; industries?: string[]; skills?: string[]; snowID?: string }) => {
  formData.value.dreamJob = value;
};

const handlePreferedLocationChange = (value: {
  id: string;
  name: string;
  city: string;
  district: string;
  zipCode: string;
  placeId: string;
  formattedAddress: string;
  country: {
    code: string;
    name: string;
  };
  state?: {
    code: string;
    name: string;
  };
  location: {
    lat: number;
    lng: number;
  };
}) => {
  formData.value.preferedLocation = value;
};

const handleWorkSetupChange = (value: WorkSetup) => {
  formData.value.workSetup = value;
};

const handleExperienceChange = (value: number) => {
  formData.value.experience = value;
};

// Methods
const handleValidationChange = (isValid: boolean) => {
  console.log(isValid)
  isFormValid.value = isValid;
  emit("validationChange", isValid);
};




// Additional initialization on mount
// (Location value initialization is handled above)

// Expose methods to parent
defineExpose({
  isValid: isFormValid, // Expose the raw ref
});
const handleFromRangeSalaryChange = (value: string) => {
  formData.value.fromRangeSalary = value;
};

const handleToRangeSalaryChange = (value: string) => {
  formData.value.toRangeSalary = value;
};
</script>

<style scoped lang="scss">
@use "../../../assets/scss/mixin" as *;

</style>
