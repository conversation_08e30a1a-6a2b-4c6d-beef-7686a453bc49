<template>
  
    <div class="form-header">
      <h1 class="form-title">Let's get to know you</h1>
      <p class="form-subtitle">
        Start with your name and where you're from — just the basics.
      </p>
    </div>
   
    <BForm
      ref="formRef"
      :validation-schema="personalInfoSchema"
      :initial-values="{
        name: formData.name,
        country: formData.country,
        phoneNumber: formData.phoneNumber,
      }"
      @validation-change="handleValidationChange"
      class="onboarding-form"
    >
      <!-- LinkedIn Button -->
      <!-- <B_Button
        type="secondary"
        size="lg"
        :is-box="false"
        :fullwidth="true"
        leftIcon="linkedin"
        class="linkedin-btn"
      >
        Fill in with LinkedIn
      </B_Button> -->

      <!-- Divider -->
      <!-- <Divider text="or" class="form-divider" /> -->

      <!-- Name Input -->
      <div class="form-group">
        <B_Input
          label="What should we call you?"
          name="name"
          placeholder="e.g. <PERSON>"
          required
          :model-value="formData.name"
          @update:model-value="handleNameChange"
        />
      </div>

      <!-- Country Selector -->
      <div class="form-group">
        <SearchInput
          searchType="places"
          :left-icon="'mapPin'"
          label="Select your country"
          name="country"
          @change="handleCountryChange"
          placeholder="e.g. United States"
          :required="true"
        />
      </div>

      <!-- Phone Number -->
      <div class="form-group">
        <PhoneInput
          :default-country="'XK'"
          label="Phone number"
          name="phoneNumber"
          placeholder="e.g. +****************"
          :model-value="formData.phoneNumber"
          @phone-validated="handlePhoneValidation"
          @country-detected="handleCountryDetection"
        />
      </div>
    </BForm>

    <!-- Form Actions -->
    <div class="form-actions">
      <B_Button
        type="primary"
        size="lg"
        :is-box="false"
      
        :disabled="!isFormValid"
        :is-loading="isLoading"
        @click="handleSubmit"
      >
        Continue
      </B_Button>
    </div>
  
</template>

<script setup lang="ts">
import SearchInput from "@/components/talentflow/search-input.vue";
import PhoneInput from "@/components/talentflow/phone-input.vue";
import { ref } from "vue";

import B_Input from "@/components/ff/input.vue";
import B_Button from "@/components/talentflow/button.vue";
import BForm from "@/components/ff/form.vue";

import { useRegistrationStore } from "@/stores/registration.store";
import { personalInfoSchema } from "@/utils/validation-schemas/onboarding/personalInfoSchema";
import { useAuthStore } from "@/stores/auth.store";
import { AuthService } from "@/services";
import { LocationPurpose } from "@/types/general.types";
const authStore = useAuthStore();
const registrationStore = useRegistrationStore();
const emit = defineEmits<{ validationChange: [isValid: boolean] }>();
const isLoading = ref(false)
// The BForm's validation status is now the single source of truth for this step's validity.
const isFormValid = ref(false);

const formRef = ref();

const handleValidationChange = (isValid: boolean) => {
  isFormValid.value = isValid;
  emit("validationChange", isValid);
};

// These handlers are still needed to update the store,
// but they no longer need to call checkOverallValidity().
const handleCountryChange = (value: any) => {
  // Only update form data when a selection is made
  formData.value.country = value;
  // The validation will be handled by vee-validate through the BForm
};

const handlePhoneValidation = (validation: {
  isValid: boolean;
  phone: any;
}) => {
  // Also save the phone number to store
  if (validation.phone) {
    // registrationStore.updatePersonalInfo("phoneNumber", validation.phone);
    formData.value.phoneNumber = validation.phone;
  }
  console.log("Phone validation:", validation);
};

const handleCountryDetection = (countryCode: string) => {
  // Optionally update selected country when detected from phone number
  console.log("Country detected from phone:", countryCode);
};

const handleNameChange = (value: string) => {
  // registrationStore.updatePersonalInfo("name", value);
  formData.value.name = value;
};

// Handle form submission and navigation
const handleSubmit = async () => {
  if (!formRef.value) return;

  // Trigger validation on all fields
  const isValid = await formRef.value.validate();

  if (isValid.valid) {
    // Ensure all current form values are saved to store before navigation
    console.log("📋 Submitting personal info:", formData.value);

    // Split the name into first and last name
    const nameParts = formData.value.name.split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    // Map the data to the required format
    const mappedData = {
      firstName,
      lastName,
      phoneNumber: formData.value.phoneNumber,
      placeId: formData.value.country?.placeId || "",
      name: formData.value.country?.name || "",
      countryCode: formData.value.country?.country?.code || "",
      countryName: formData.value.country?.country?.name || "",
      city: formData.value.country?.city || "",
      district: formData.value.country?.district || "",
      zipCode: formData.value.country?.zipCode || "",
      formattedAddress: formData.value.country?.formattedAddress || "",
      latitude: formData.value.country?.location?.lat || 0,
      longitude: formData.value.country?.location?.lng || 0,
      purpose: LocationPurpose.ORIGIN,
      stateCode: formData.value.country?.state?.code || "",
      stateName: formData.value.country?.state?.name || "",
    };

    registrationStore.updatePersonalInfo("name", formData.value.name);
    registrationStore.updatePersonalInfo("country", formData.value.country);
    registrationStore.updatePersonalInfo(
      "phoneNumber",
      formData.value.phoneNumber
    );
    isLoading.value = true 
    let result = await AuthService.update_onboarding_step_1(mappedData);
    if (result.success) {
      // Log store state after update
      console.log(
        "🏪 Store personal info after update:",
        registrationStore.personalInfo
      );

      // Navigate to next step
      registrationStore.goToNextStep();
    }
     isLoading.value = false 
  } else {
    console.warn("⚠️ Form validation failed, cannot submit");
  }
};

const formData = ref({
  name: authStore.user?.fullName || "",
  country: {
    city: "",
    country: {
      code: "",
      name: "",
    },
    district: "",
    formattedAddress: "",
    id: "",
    location: {
      lat: 0,
      lng: 0,
    },
    name: "",
    placeId: "",
    state: {
      code: "",
      name: "",
    },
    zipCode: "",
  },
  phoneNumber: "",
});
defineExpose({
  isValid: isFormValid, // Expose the raw ref
});
</script>

<style scoped lang="scss">
@use "../../../assets/scss/mixin" as *;


</style>
