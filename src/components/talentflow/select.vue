<template>
  <div class="select-field">
    <label v-if="label" class="form-label">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>

    <SuggestionsDropdown
      :options="options"
      :search-value="''"
      :show="showDropdown"
      :model-value="modelValue"
      mode="select"
      :placeholder="placeholder"
      :max-suggestions="maxOptions"
      :has-error="!!errorMessage"
      @select="handleSelect"
      @update:show="(value) => (showDropdown = value)"
    />

    <div v-if="errorMessage" class="input-error">
      <p class="error-text">{{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import SuggestionsDropdown from "./suggestions-dropdown.vue";

interface Option {
  id: string;
  label: string;
}

interface Props {
  label?: string;
  modelValue?: string;
  options: Option[];
  placeholder?: string;
  required?: boolean;
  errorMessage?: string;
  maxOptions?: number;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "Select an option",
  maxOptions: 10,
  required: false,
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
  change: [value: string];
}>();

const showDropdown = ref(false);

const handleSelect = (option: { id: string; label: string } | string) => {
  const value = typeof option === "string" ? option : option.id;
  emit("update:modelValue", value);
  emit("change", value);
};
</script>

<style scoped lang="scss">
.select-field {
  .form-label {
    font-family: var(--font-2);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    color: #000;
    line-height: 24px;
    display: inline-flex;
    margin-bottom: 0.5rem;

    .required {
      color: #ef4444;
    }
  }

  .input-error {
    margin-top: 0.5rem;

    .error-text {
      color: #ef4444;
      font-size: 0.875rem;
      font-weight: 500;
      margin: 0;
    }
  }
}
</style>
