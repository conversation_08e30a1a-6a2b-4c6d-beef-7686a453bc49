<template>
  <div
    :class="[
      'ff_input',
      extraClass,
      { 'has-error': hasError, disabled: disabled },
    ]"
  >
    <label :for="id" v-if="label">{{ label }}</label>

    <div
      class="ff-input-container input_with_phone_number"
      :class="{
        'input-with-left-icon': showCountrySelector,
        'has-country-dropdown': dropdownOpen,
      }"
    >
      
      
      <!-- Country Selector as Left Icon -->
      <span
        v-if="showCountrySelector"
        class="phone_prefix"
        @click="toggleCountryDropdown"
      >
        
          <span class="country-flag">{{
            selectedCountryData?.flag || "🌍"
          }}</span>
          <span class="country-code"
            >+{{ selectedCountryData?.dialCode?.replace("+", "") || "1" }}</span
          >
        
      </span>

      <input
        :autocomplete="autocomplete"
        :type="type"
        ref="inputElement"
        :id="id"
        :placeholder="placeholder"
        :value="displayValue"
        @input="updateValue"
        @focus="onFocus"
        @blur="emitBlur"
        @keydown="onKeyDown"
        @keydown.tab="onTabPressed"
        @keydown.enter="onEnterPressed"
        :disabled="disabled"
      />

      <!-- Country Dropdown -->
      <div v-if="dropdownOpen && showCountrySelector" class="suggestions-dropdown">
        <div class="search-container">
          <input
            ref="searchInput"
            v-model="searchQuery"
            placeholder="Search countries..."
            class="country-search"
            @keydown.escape="closeDropdown"
            @keydown.enter="handleSearchEnter"
            @keydown="handleKeyDown"
          />
        </div>
       <div
            v-for="(country, index) in filteredCountries"
            :key="country.code"
            class="suggestion-item"
            :class="{ 'highlighted': index === highlightedIndex }"
            @click="selectCountry(country)"
            :ref="el => { if (index === highlightedIndex) highlightedItem = el as HTMLElement }"
          >
            <span class="country-flag">{{ country.flag }}</span>
            <span class="country-name">{{ country.name }}</span>
            <span class="country-dial-code">{{ country.dialCode }}</span>
          </div>
      </div>
    </div>

    <div class="input_errors" v-if="displayError">
      <p class="error">{{ displayError }}</p>
    </div>
    <template v-else-if="hint">
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
import {
  ref,
  watch,
  computed,
  inject,
  onMounted,
  nextTick,
  onUnmounted,
} from "vue";
import {
  parsePhoneNumberFromString,
  formatIncompletePhoneNumber,
} from "libphonenumber-js";
import type { CountryCode } from "libphonenumber-js";
import { countries } from "country-codes-flags-phone-codes";
import { useField } from "vee-validate";

const inputElement = ref<HTMLElement | null>(null);
const searchInput = ref<HTMLInputElement | null>(null);
const dropdownOpen = ref(false);
const searchQuery = ref("");
const highlightedIndex = ref(-1);
const highlightedItem = ref<HTMLElement | null>(null);
const isInitialScroll = ref(false);

const props = defineProps({
  modelValue: String,
  value: String,
  label: String,
  type: {
    type: String as PropType<
      "text" | "tel" | "email" | "number" | "password" | "url"
    >,
    default: "tel",
  },
  hint: String,
  id: String,
  placeholder: String,
  disabled: Boolean,
  autocomplete: String,
  extraClass: String,
  errorMessage: String,
  defaultCountry: {
    type: String as PropType<CountryCode>,
    default: "US",
  },
  formatAsYouType: {
    type: Boolean,
    default: true,
  },
  showCountrySelector: {
    type: Boolean,
    default: true,
  },

  // Validation props
  validationType: {
    type: String as PropType<
      "email" | "password" | "phone" | "required" | "none"
    >,
    default: "phone",
  },
  validationMessage: String,

  // Form integration props
  name: {
    type: String,
    required: false,
  },
});

function focus() {
  inputElement.value?.focus();
}

function clearValidationError() {
  internalError.value = "";
}

// Convert country-codes-flags-phone-codes format to our format
const availableCountries = computed(() => {
  return countries.map((country) => ({
    name: country.name,
    code: country.code as CountryCode,
    dialCode: country.dialCode,
    flag: country.flag,
  }));
});

// Selected country state
const selectedCountryCode = ref<CountryCode>(props.defaultCountry);

// Find selected country data
const selectedCountryData = computed(() => {
  return availableCountries.value.find(
    (country) => country.code === selectedCountryCode.value
  );
});

// Filter countries based on search
const filteredCountries = computed(() => {
  if (!searchQuery.value) {
    return availableCountries.value;
  }
  return availableCountries.value.filter(
    (country) =>
      country.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      country.code.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      country.dialCode.includes(searchQuery.value)
  );
});

// Inject form context if available
const formContext = inject("formContext", null);

// Check if we should use vee-validate (when inside BForm and name is provided)
const shouldUseVeeValidate = formContext && props.name;

// Setup vee-validate field if inside a form
const veeField = shouldUseVeeValidate
  ? useField(props.name, undefined, {
      syncVModel: true,
      initialValue: props.modelValue || "",
    })
  : null;

// Use vee-validate value and error if available, otherwise use internal state
const internalValueRef = shouldUseVeeValidate ? null : ref(props.modelValue);
const internalError = ref<string>("");

// Computed property for the current value
const internalValue = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return veeField.value.value;
  }
  return internalValueRef?.value;
});

// Parse phone number and provide formatting
const parsedPhone = computed(() => {
  if (!internalValue.value) return null;
  return parsePhoneNumberFromString(
    internalValue.value,
    selectedCountryCode.value
  );
});

// Display formatted value
const displayValue = computed(() => {
  if (!internalValue.value) return "";

  let valueToFormat = internalValue.value;
  const dialCode = selectedCountryData.value?.dialCode;

  // If the internal value starts with the selected country's dial code,
  // strip it for display, as it's already shown in the prefix.
  if (dialCode && valueToFormat.startsWith(dialCode)) {
    valueToFormat = valueToFormat.substring(dialCode.length);
  }

  // Also, ensure no leading '+' in the part we format if it's already handled by the prefix
  if (valueToFormat.startsWith('+')) {
    valueToFormat = valueToFormat.substring(1);
  }

  // Format the remaining national number part using the selected country's rules.
  // This should now only contain the national digits.
  return formatIncompletePhoneNumber(valueToFormat, selectedCountryCode.value);
});

const onFocus = () => {};

const onKeyDown = (event: KeyboardEvent) => {
  // Allow only numbers, +, -, space, parentheses, and control keys
  const allowedKeys = [
    "Backspace",
    "Delete",
    "Tab",
    "Escape",
    "Enter",
    "Home",
    "End",
    "ArrowLeft",
    "ArrowRight",
    "ArrowUp",
    "ArrowDown",
  ];

  const isNumberKey = /^[0-9]$/.test(event.key);
  const isSpecialChar = /^[\+\-\s\(\)]$/.test(event.key);
  const isAllowedKey = allowedKeys.includes(event.key);
  const isCtrlCmd = event.ctrlKey || event.metaKey;

  if (!isNumberKey && !isSpecialChar && !isAllowedKey && !isCtrlCmd) {
    event.preventDefault();
  }
};

const hasError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const displayError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const emit = defineEmits([
  "update:modelValue",
  "change",
  "blur",
  "tabPressed",
  "enterPressed",
  "phoneValidated",
  "countryChanged",
]);

const validatePhoneNumber = (phoneNumber: string) => {
  if (!phoneNumber) {
    internalError.value = "";
    return;
  }

  const parsed = parsePhoneNumberFromString(
    phoneNumber,
    selectedCountryCode.value
  );

  if (!parsed) {
    internalError.value = "Invalid phone number format";
    // Explicitly emit null for phone when parsing fails
    emit("phoneValidated", { isValid: false, phone: null });
    return;
  }

  if (!parsed.isValid()) {
    internalError.value = "Please enter a valid phone number";
    // Explicitly emit parsed.number as a string when invalid
    emit("phoneValidated", { isValid: false, phone: parsed.number || null });
    return;
  }

  internalError.value = "";
  // Explicitly emit parsed.number as a string when valid
  emit("phoneValidated", { isValid: true, phone: parsed.number });

  // Emit country detection if it differs from selected
  if (parsed.country && parsed.country !== selectedCountryCode.value) {
    // Auto-update country if detected from phone number
    selectedCountryCode.value = parsed.country;
    emit("countryChanged", parsed.country);
  }
};

const updateValue = (event: Event) => {
  const rawValue = (event.target as HTMLInputElement).value;

  // Store the raw value: allow digits and a single leading '+'
  // The displayValue computed property will handle visual formatting including country code
  let cleanValue = rawValue.replace(/[^\d\+]/g, "");
  if (cleanValue.startsWith('+')) {
    cleanValue = '+' + cleanValue.substring(1).replace(/\+/g, '');
  }

  if (shouldUseVeeValidate && veeField) {
    // If using vee-validate, update the field value
    veeField.value.value = cleanValue;
  } else {
    // Traditional approach
    if (internalValueRef) {
      internalValueRef.value = cleanValue;
    }

    // Clear error when user starts typing (if there's an error)
    if (internalError.value && cleanValue.length > 0) {
      internalError.value = "";
    }
  }

  // Validate phone number
  validatePhoneNumber(cleanValue);

  // Emit the clean value, which will be validated by the parent schema
  emit("update:modelValue", cleanValue);
  emit("change", cleanValue);
};

// Country dropdown methods
const toggleCountryDropdown = () => {
  dropdownOpen.value = !dropdownOpen.value;
  if (dropdownOpen.value) {
    // Find the index of the currently selected country in the filtered list
    const currentCountryIndex = filteredCountries.value.findIndex(
      country => country.code === selectedCountryCode.value
    );
    highlightedIndex.value = currentCountryIndex >= 0 ? currentCountryIndex : -1;
    
    nextTick(() => {
      searchInput.value?.focus();
      // Scroll the selected country into view if it exists
      if (currentCountryIndex >= 0) {
        isInitialScroll.value = true;
        scrollHighlightedIntoView();
      }
    });
  }
};

const closeDropdown = () => {
  dropdownOpen.value = false;
  searchQuery.value = "";
  highlightedIndex.value = -1;
};

const selectCountry = (country: any) => {
  selectedCountryCode.value = country.code;
  closeDropdown();
  emit("countryChanged", country.code);

  // Re-validate current phone number with new country
  if (internalValue.value) {
    validatePhoneNumber(internalValue.value);
  }

  // Focus back to phone input
  nextTick(() => {
    inputElement.value?.focus();
  });
};

// Close dropdown when clicking outside
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".ff_input")) {
    closeDropdown();
  }
};

const emitBlur = () => {
  // Validate on blur
  if (internalValue.value) {
    validatePhoneNumber(internalValue.value);
  }
  emit("blur");
};

const onTabPressed = (event: any) => {
  emit("tabPressed", event);
};

const onEnterPressed = (event: any) => {
  emit("enterPressed", event);
};

const handleSearchEnter = (event: KeyboardEvent) => {
  event.preventDefault();
  if (filteredCountries.value.length > 0) {
    if (highlightedIndex.value >= 0) {
      selectCountry(filteredCountries.value[highlightedIndex.value]);
    } else {
      selectCountry(filteredCountries.value[0]);
    }
  }
};

const scrollHighlightedIntoView = () => {
  if (highlightedItem.value) {
    highlightedItem.value.scrollIntoView({
      block: 'nearest',
      behavior: isInitialScroll.value ? 'auto' : 'smooth'
    });
    isInitialScroll.value = false;
  }
};

const handleKeyDown = (event: KeyboardEvent) => {
  if (!dropdownOpen.value) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      if (highlightedIndex.value < filteredCountries.value.length - 1) {
        highlightedIndex.value++;
      } else {
        highlightedIndex.value = 0;
      }
      nextTick(scrollHighlightedIntoView);
      break;
    case 'ArrowUp':
      event.preventDefault();
      if (highlightedIndex.value > 0) {
        highlightedIndex.value--;
      } else {
        highlightedIndex.value = filteredCountries.value.length - 1;
      }
      nextTick(scrollHighlightedIntoView);
      break;
    case 'Enter':
      handleSearchEnter(event);
      break;
  }
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (shouldUseVeeValidate && veeField) {
      veeField.value.value = newValue || "";
    } else {
      if (internalValueRef) {
        internalValueRef.value = newValue;
      }
    }
  }
);

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

defineExpose({ focus, clearValidationError });
</script>

<style scoped>
.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  display: flex;
  flex-direction: column;
}

.search-container {
  padding: 8px;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.country-search {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.countries-list {
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
  background-color: #f5f5f5;
}

.country-flag {
  font-size: 1.2em;
}

.country-name {
  flex: 1;
}

.country-dial-code {
  color: #666;
}
</style>
