<template>
  <div class="sidebar">
    <div class="logo-container">
      <LogoImage size="xs" :is-icon="true" />
    </div>
    <div class="sidebar-buttons">
      <B_Button
        type="primary"
        size="md"
        :is-box="true"
        leftIcon="penLine"
        to="/user/settings"
      />
      <B_Button
        type="flat"
        size="md"
        :is-box="true"
        leftIcon="layoutGrid"
        to="/user/settings"
      />
      <B_Button
        type="flat"
        size="md"
        :is-box="true"
        leftIcon="listIcon"
        to="/user/settings"
      />
      <B_Button
        type="flat"
        size="md"
        :is-box="true"
        leftIcon="sendIcon"
        to="/user/settings"
      />
      <B_Button
        type="flat"
        size="md"
        :is-box="true"
        leftIcon="moonIcon"
        to="/user/settings"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import B_<PERSON><PERSON> from "@/components/talentflow/button.vue";
import LogoImage from "@/components/ff/logo-image.vue";
</script>

<style lang="scss" scoped>
.sidebar {
  .logo-container {
    margin-bottom: 1rem;
    margin-top: 1rem;

    padding: 0 !important;
  }

  .sidebar-buttons {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    gap: 1rem;
  }
}
</style>
