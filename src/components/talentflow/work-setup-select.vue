<template>
  <div class="form-group">
    <label v-if="label" class="form-label">{{ label }}</label>
    <div class="work-setup-options">
      <button
        type="button"
        class="setup-option"
        :class="{ active: modelValue === 'REMOTE' }"
        @click="selectOption('REMOTE')"
      >
        <svg
          class="setup-icon"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9 22V12H15V22"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <span>Remote</span>
      </button>
      <button
        type="button"
        class="setup-option"
        :class="{ active: modelValue === 'HYBRID' }"
        @click="selectOption('HYBRID')"
      >
        <div class="inline_svg">
          <svg
            class="setup-icon"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M9 22V12H15V22"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <svg
            width="24"
            height="24"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.66 7.33301L2 9.99967L4.66 12.6663V10.6663H9.33333V9.33301H4.66V7.33301ZM14 5.99967L11.34 3.33301V5.33301H6.66667V6.66634H11.34V8.66634L14 5.99967Z"
              fill="#323232"
            />
          </svg>

          <svg
          class="setup-icon"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M19 21V5C19 4.46957 18.7893 3.96086 18.4142 3.58579C18.0391 3.21071 17.5304 3 17 3H7C6.46957 3 5.96086 3.21071 5.58579 3.58579C5.21071 3.96086 5 4.46957 5 5V21"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M3 21H21"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9 7H15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9 11H15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9 15H15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        </div>

        <span>Hybrid</span>
      </button>
      <button
        type="button"
        class="setup-option"
        :class="{ active: modelValue === 'ON_SITE' }"
        @click="selectOption('ON_SITE')"
      >
        <svg
          class="setup-icon"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M19 21V5C19 4.46957 18.7893 3.96086 18.4142 3.58579C18.0391 3.21071 17.5304 3 17 3H7C6.46957 3 5.96086 3.21071 5.58579 3.58579C5.21071 3.96086 5 4.46957 5 5V21"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M3 21H21"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9 7H15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9 11H15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9 15H15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <span>On site</span>
      </button>
    </div>
    <div v-if="errorMessage" class="input-error">
      <p class="error-text">{{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, computed } from "vue";
import { useField } from "vee-validate";
import type { UserProfile } from "@/stores/types/auth.types";

const props = defineProps({
  modelValue: {
    type: String as () => UserProfile["workSetup"],
    default: "",
  },
  label: {
    type: String,
    default: "",
  },
  name: {
    type: String,
    required: false,
  },
});

const emit = defineEmits<{
  "update:modelValue": [value: UserProfile["workSetup"]];
  change: [value: UserProfile["workSetup"]];
}>();

// Inject form context and setup vee-validate field
const formContext = inject("formContext", null);
const shouldUseVeeValidate = formContext && props.name;

const veeField = shouldUseVeeValidate
  ? useField<UserProfile["workSetup"]>(props.name, undefined, {
      syncVModel: true,
      initialValue: props.modelValue,
    })
  : null;

const selectOption = (option: UserProfile["workSetup"]) => {
  if (veeField) {
    veeField.value.value = option;
  }
  emit("update:modelValue", option);
  emit("change", option);
};

// Use vee-validate error message if available
const errorMessage = computed(() => {
  return veeField ? veeField.errorMessage.value : "";
});
</script>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-family: var(--font-2);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: #000;
  line-height: 24px;
  margin-bottom: 8px;
  display: block;
}

.work-setup-options {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  .inline-svg {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .setup-option {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: white;
    color: #6b7280;
    transition: all 0.2s ease;
    cursor: pointer;
    font-family: var(--font-2);
    align-items: flex-start;
    &:hover {
      border-color: #000000;
      color: #000;
    }

    &.active {
      border-color: #000;
      background-color: rgba(230, 230, 230, 0.05);
      color: #000;
    }

    .setup-icon {
      width: 24px;
      height: 24px;
    }

    span {
      padding-top: 20px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.input-error {
  margin-top: 0.5rem;

  .error-text {
    font-size: 0.875rem;
    color: #ef4444;
  }
}

// Responsive design
@media (max-width: 768px) {
  .work-setup-options {
    flex-direction: column;
    gap: 0.75rem;

    .setup-option {
      flex-direction: row;
      justify-content: flex-start;
      padding: 0.75rem 1rem;

      .setup-icon {
        width: 20px;
        height: 20px;
      }
    }
  }
}
</style>
