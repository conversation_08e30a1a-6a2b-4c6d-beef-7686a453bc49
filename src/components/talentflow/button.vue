<template>
  
  <button
    v-if="isSubmit"
    :class="`${defaultStyle} ${selectedStyle} ${selectedSize} ${isActive} ${hasInvertedColor} ${isBoxBtn} ${selectedVariant} ${isFullWidth}`"
    :href="href"
    :disabled="disabled"
    type="submit"
  >
    <template v-if="isLoading">
      <Loader :variant="type" />
    </template>
    <template v-else>
      <component
        v-if="leftIcon && iconMap[leftIcon]"
        :is="iconMap[leftIcon]"
        v-bind="defaultIconProps"
        class="btn-left-icon"
      />
      <span
        ><slot>{{ text }}</slot></span
      >
      <component
        v-if="rightIcon && iconMap[rightIcon]"
        :is="iconMap[rightIcon]"
        v-bind="defaultIconProps"
        class="btn-right-icon"
      />
    </template>
  </button>
  <router-link
    v-else-if="isRoute"
    :to="to"
    :class="`${defaultStyle} ${selectedStyle} ${selectedSize} ${isDisabled} ${isActive} ${isBoxBtn} ${selectedVariant} ${isFullWidth}`"
  >
    <template v-if="isLoading">
      <Loader :variant="type" />
    </template>
    <template v-else>
      <component
        v-if="leftIcon && iconMap[leftIcon]"
        :is="iconMap[leftIcon]"
        v-bind="defaultIconProps"
        class="btn-left-icon"
      />
      <span
        ><slot>{{ text }}</slot></span
      >
      <component
        v-if="rightIcon && iconMap[rightIcon]"
        :is="iconMap[rightIcon]"
        v-bind="defaultIconProps"
        class="btn-right-icon"
      />
    </template>
  </router-link>
  <a
    v-else-if="isLink"
    :class="`${defaultStyle} ${selectedStyle} ${selectedSize} ${isActive} ${isBoxBtn} ${selectedVariant} ${isFullWidth}`"
    :href="href"
    @click="onClick"
  >
    <template v-if="isLoading">
      <Loader :variant="type" />
    </template>
    <template v-else>
      <component
        v-if="leftIcon && iconMap[leftIcon]"
        :is="iconMap[leftIcon]"
        v-bind="defaultIconProps"
        class="btn-left-icon"
      />
      <span
        ><slot>{{ text }}</slot></span
      >
      <component
        v-if="rightIcon && iconMap[rightIcon]"
        :is="iconMap[rightIcon]"
        v-bind="defaultIconProps"
        class="btn-right-icon"
      />
    </template>
  </a>

  <button
    v-else
    :class="`${defaultStyle} ${selectedStyle} ${selectedSize} ${isActive} ${isBoxBtn} ${selectedVariant} ${isFullWidth}`"
    :href="href"
    :disabled="disabled"
    @click="onClick"
  >
    <template v-if="isLoading">
      <Loader :variant="type" />
    </template>
    <template v-else>
      <component
        v-if="leftIcon && iconMap[leftIcon]"
        :is="iconMap[leftIcon]"
        v-bind="defaultIconProps"
        class="btn-left-icon"
      />
      <span
        ><slot>{{ text }}</slot></span
      >
      <component
        v-if="rightIcon && iconMap[rightIcon]"
        :is="iconMap[rightIcon]"
        v-bind="defaultIconProps"
        class="btn-right-icon"
      />
    </template>
  </button>
</template>

<script setup lang="ts">
import Loader from "@55/loader.vue"
import { ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  iconMap,
  type IconName,
  type IconProps,
  defaultIconProps,
} from "../ff/iconMap";
import type {
  ButtonProps,
  ButtonType,
  ButtonSize,
  ButtonVariant,
} from "../../types/componentTypes";

const props = withDefaults(defineProps<ButtonProps>(), {
  text: "",
  to: "",
  type: "none",
  size: "md",
  disabled: false,
  colorInverted: false,
  isButton: false,
  isRoute: false,
  isLink: false,
  isBox: false,
  isLoading: false,
  active: false,
  isSubmit: false,
  rightIcon: undefined,
  leftIcon: undefined,
  fullwidth: false,
});

const emit = defineEmits(["click"]);

const router = useRouter();

const defaultStyle = ref("btn");
const styles: Record<string, string> = {
  none: "",
  primary: "btn btn--primary",
  secondary: "btn btn--secondary",
  white: "btn btn--white",
  flat: "btn btn--flat",
  dark: "btn btn--dark",
  error: "btn btn--error",
  bordered: " btn--bordered",
};

const variants = {
  white: "variant--white",
};
const sizes: Record<string, string> = {
  lg: "btn-large",
  md: "btn-medium",
  sm: "btn-small",
};

const selectedVariant = computed(() => {
  if (props.variant == "white") {
    return "variant--white";
  }
  return "";
});

const selectedStyle = computed(() => styles[props.type]);
const selectedSize = computed(() => sizes[props.size] || sizes.lg);
const isBoxBtn = computed(() => {
  return props.isBox ? "btn--box" : null;
});
const isDisabled = computed(() => (props.disabled ? "is_disabled" : null));
const isActive = computed(() => (props.active ? "is_active" : null));
const isFullWidth = computed(() => (props.fullwidth ? "fullwidth" : null));
const hasInvertedColor = computed(() =>
  props.colorInverted ? "invert_color" : null
);

const onClick = (event: Event) => {
  if (props.isButton) {
    emit("click");
  } else if (props.to) {
    router.push(props.to);
  } else {
    if (!props.href) {
      event.preventDefault();
      emit("click");
    }
  }
};
</script>

<style lang="scss" scoped>
/* Your styles here */
</style>
