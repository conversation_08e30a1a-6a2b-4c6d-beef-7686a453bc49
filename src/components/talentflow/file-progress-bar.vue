<template>
  <div class="file-progress-container">
    <div class="file-info">
      <div class="file-details">
        <div class="file-icon">
          <component
            v-if="leftIcon && iconMap[leftIcon]"
            :is="iconMap[leftIcon]"
            v-bind="defaultIconProps"
          />
          <ff-icon v-else icon="job" />
        </div>
        <div class="file-header">
          <div class="file-text">
            <div class="file-center">
              <h6 class="file-name">{{ fileName }}</h6>
              <p class="file-size">{{ formattedFileSize }}</p>
            </div>
            <div class="file-actions-left">
              <button
                class="upload-delete-btn"
                @click="$emit('delete')"
                :disabled="isUploading"
              >
                <component
                  v-if="deleteIcon && iconMap[deleteIcon]"
                  :is="iconMap[deleteIcon]"
                  v-bind="defaultIconProps"
                />
                <component
                  v-else-if="rightIcon && iconMap[rightIcon]"
                  :is="iconMap[rightIcon]"
                  v-bind="defaultIconProps"
                />
                <ff-icon v-else icon="x" />
              </button>
            </div>
          </div>

          <div class="progress-container">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ '--progress-width': `${progress}%` }"
              ></div>
            </div>
            <span class="progress-text">{{ progress }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import {
  iconMap,
  type IconName,
  defaultIconProps,
} from "@/components/ff/iconMap";

interface Props {
  fileName: string;
  fileSize: number; // in bytes
  progress: number; // 0-100
  isUploading?: boolean;
  leftIcon?: IconName;
  rightIcon?: IconName;
  deleteIcon?: IconName;
}

const props = withDefaults(defineProps<Props>(), {
  isUploading: false,
  deleteIcon: "x",
});

const emit = defineEmits<{
  delete: [];
}>();

// Format file size to human readable format
const formattedFileSize = computed(() => {
  const bytes = props.fileSize;
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
});
</script>

<style scoped lang="scss">
.file-progress-container {
  /* File upload item base */
  box-sizing: border-box;

  /* Auto layout */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  gap: 4px;
  isolation: isolate;

  position: relative;
  width: 100%;
  max-width: 657px;
  height: 96px;

  background: #ffffff;
  border: 1px solid #eaecf0;
  border-radius: 12px;
  margin-bottom: 12px;
  .file-info {
    display: flex;
    align-items: start;
    justify-content: space-between;
    width: 100%;
    flex: 1;
    .file-details {
      display: flex;
      align-items: start;
      width: 100%;
      .file-icon {
        display: flex;
        align-items: start;
        justify-content: center;
        width: 40px;
        height: 40px;

        border-radius: 8px;
        color: #98a2b3;
        flex-shrink: 0;
      }
      .file-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 4px;
        .progress-container {
          display: flex;
          align-items: center;
          gap: 12px;
          width: 100%;
        }

        .file-text {
          display: flex;
          align-items: start;
          justify-content: space-between;
          width: 100%;
          .file-center {
            .file-name {
              font-size: 14px;
              font-weight: 400;
              color: #344054;
              margin: 0 0 2px 0;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .file-size {
              font-size: 14px;
              color: #475467;
              margin: 0;
              line-height: 20px;
            }
          }
        }
      }
    }
  }
}

.upload-delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #475467;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #f9fafb;
    color: #344054;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f2f4f7;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: #121212;
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
  width: var(--progress-width, 0%);
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #344054;
  min-width: 40px;
  text-align: right;
}
</style>
