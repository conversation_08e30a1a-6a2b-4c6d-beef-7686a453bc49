import { z } from "zod";
import { toTypedSchema } from "@vee-validate/zod";

// Job selection schema
const jobSelectionSchema = z.object({
  id: z.string(),
  name: z.string(),
  seniority: z.string().optional(),
  industries: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  snowID: z.string().optional()
});


// Schema for vee-validate/BForm integration - excluding location (handled manually)
export const jobPreferencesSchema = toTypedSchema(
  z.object({
    dreamJob: z.union([
      z.string().min(2, "Please enter your dream job"),
      jobSelectionSchema,
    ]),
    preferedLocation: z.string().min(1, "Please select your prefered location"),
    experience: z.number().min(0, "Experience must be 0 or greater").optional(),
    workSetup: z.string().optional(),
    // Salary field - string based on store structure (format: "USD:50000-80000")
   
  })
);
