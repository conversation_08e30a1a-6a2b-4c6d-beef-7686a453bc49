import { z } from "zod";
import { toTypedSchema } from "@vee-validate/zod";
import { parsePhoneNumberFromString } from "libphonenumber-js";

const phoneValidator = z
  .string()
  .min(1, "Please enter your phone number")
  .refine(
    (phoneNumber) => {
      if (!phoneNumber) return false;

      // Try to parse without country first (for international format like +1...)
      const parsedInternational = parsePhoneNumberFromString(phoneNumber);
      if (parsedInternational && parsedInternational.isValid()) {
        return true;
      }

      // If that fails, try with common countries as fallback
      const commonCountries = ["US", "CA", "GB", "AU", "DE", "FR"];
      for (const country of commonCountries) {
        const parsed = parsePhoneNumberFromString(phoneNumber, country as any);
        if (parsed && parsed.isValid()) {
          return true;
        }
      }

      return false;
    },
    {
      message: "Please enter a valid phone number",
    }
  );

export const personalInfoSchema = toTypedSchema(
  z.object({
    name: z
      .string()
      .min(1, "Please enter your name")
      .min(2, "Name must be at least 2 characters"),
    country: z.string().min(1, "Please select your country"),
    phoneNumber: phoneValidator,
  })
);
