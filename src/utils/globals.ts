
import { format, formatDistanceToNow, isAfter, subDays } from "date-fns";


// Format date based on date-fns  plugin date is "createdAt": "2025-03-31T18:26:28.200Z",
export const formatDate = (date: string) => {
  if (!date) return "";
  const dateObj = new Date(date);
  return format(dateObj, "dd/MM/yyyy, HH:mm");
};

export const formatDateDistance = (date?: string) => {
  if (!date) return "";
  const dateObj = new Date(date);
  const sevenDaysAgo = subDays(new Date(), 7);

  if (isAfter(dateObj, sevenDaysAgo)) {
    return formatDistanceToNow(dateObj, { addSuffix: true });
  }

  return format(dateObj, "dd/MM/yyyy, HH:mm");
};

export const formatFileSize = (sizeInBytes?: number): string => {
  if (!sizeInBytes) return "Unknown";

  const sizeInMB = sizeInBytes / (1024 * 1024);
  return `${sizeInMB.toFixed(2)} MB`;
};
