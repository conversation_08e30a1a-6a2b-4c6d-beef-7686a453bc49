<script setup lang="ts">
import { WalletCards } from "lucide-vue-next";

import { useAuthStore } from "@/stores/auth.store";
import { AuthService } from "@/services";
const authStore = useAuthStore();

// const handleLogout = async () => {
//   try {
//     await AuthService.logout();
//     router.push("/auth/login");
//   } catch (error) {
//     console.error("Logout failed:", error);
//   }
// };

// const handleAccount = async () => {
//   router.push("/account");
// };
</script>

<template>admin Layout</template>
