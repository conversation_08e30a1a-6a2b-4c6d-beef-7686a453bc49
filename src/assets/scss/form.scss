.icon_hover {
  border-radius: 50%;
  text-align: center;
  padding: 0;
  background: var(--color--primary-lighter);
  color: var(--color--primary);
}

.form_button {
  justify-content: flex-end;
  display: flex;
}

.ff_label_with_action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  a {
    margin: 0 !important;
    font-weight: 500;
    padding-right: 4px;
    font-size: 16px;
    cursor: pointer;
    color: var(--color--primary);
  }
  label {
    margin: 0;
  }
}

.input_errors {
  display: flex;

  p {
    margin: 0 !important;
    font-size: 14px;
    text-align: left !important;
    font-weight: 500;
    padding-left: 7px;

    &.error {
      color: var(--color--error);
    }

    &.hint {
      color: #8d8989;
    }
  }
}
label {
  font-family: var(--font-2);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: #000;
  line-height: 24px;
  display: inline-flex;
  margin-bottom: 5px;
}
.ff_input {
  display: flex;
  gap: 5px;
  flex-direction: column;
  width: 100%;
  border-radius: 6px;
  &.has-error {
    input {
      background: #ffffff;
      /* Input/error */
      box-shadow: 0px 0px 0px 4px rgba(221, 82, 76, 0.2),
        0px 1px 1px rgba(170, 46, 38, 0.2),
        0px 0px 0px 1px rgba(170, 46, 38, 0.5),
        0px 1px 3px rgba(170, 46, 38, 0.1);
      &:focus {
        box-shadow: 0px 0px 0px 4px rgba(221, 82, 76, 0.2),
          0px 1px 1px rgba(170, 46, 38, 0.2),
          0px 0px 0px 1px rgba(170, 46, 38, 0.5),
          0px 1px 3px rgba(170, 46, 38, 0.1);
      }
    }
  }
  &.disabled {
    svg {
      color: #a1a1a9;
    }
  }
  .input_password_visibility {
    position: absolute;
    font-size: 21px;

    width: 40px;
    height: 40px;
    top: 4px;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    right: 10px;
    cursor: pointer;
    &:hover {
      @extend .icon_hover;
    }
  }
  &.has-autocompleted {
    position: relative;
  }
  &.nm {
    margin: 0;
  }
  .input-autocomplete {
    position: absolute;
    left: 0;
    right: 0;
    z-index: 99;
    top: 14px;
    .autocomplete-item {
      padding: 5px 0;
    }
    .clear_input_value {
      top: 34px;
    }
  }
  &.has-range-slider {
    input {
      border: none;
      width: 100%;
      display: none;
    }
    .range-slider {
      width: 100%;
      &.slider {
      }
    }
  }

  .ff-input-container {
    width: 100%;
    &.both-directions {
      input {
        padding-left: 40px !important;
      }
    }
    textarea {
      min-height: 300px;
    }
    .loader.loader--primary[data-v-78cb8b9a] {
      border-left-color: var(--color--primary) !important;
      border-top-color: rgb(213 213 213 / 50%) !important;
      border-right-color: rgb(213 213 213 / 50%) !important;
      border-bottom-color: rgb(213 213 213 / 50%) !important;
    }
  }
  .ff-input-with-postfix {
    height: 56px;
    // border: 2px solid #DEDFE3;
    box-shadow: 0px 0px 0px 1px rgba(18, 18, 18, 0.1),
      0px 1px 1px 0px rgba(18, 18, 18, 0.1);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    input {
      border: none;
      background: none;
      border-radius: 0;
      box-shadow: none;
    }
    .postfix_value {
      background: #f8f8f8;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 6px;
      margin-right: 0;
      border-radius: 10px;
      font-size: 12px;
      font-weight: 600;
      min-width: 50px;
      border-radius: 0;
      text-align: center;
      justify-content: center;
      padding-left: 22px;
      padding-right: 22px;
    }
  }
  .ff-input-with-icon {
    position: relative;
    svg {
      width: 17px;
    }
    .ff-icon {
      position: relative;
      display: flex;
    }
    &.input-with-left-icon {
      .left-icon {
        position: absolute;
        height: 100%;
        display: flex;
        align-items: center;
        width: 35px;
        justify-content: center;
        left: 0;
        top: 0;
        &.cursor {
          cursor: pointer;
        }
      }
      input {
        padding-left: 35px;
      }
    }
    &.input-with-right-icon {
      .right-icon {
        position: absolute;
        height: 100%;
        display: flex;
        align-items: center;
        width: 35px;
        justify-content: center;
        right: 0;
        top: 0;
        &.cursor {
          cursor: pointer;
          .ff-icon {
            background: #f1f1f1;
            border-radius: 50px;
            padding: 4px;
            right: 7px;
          }
        }
      }
      input {
        padding-right: 40px;
      }
    }
  }
  .input-with-left-leading-text {
    position: relative;
    .left-leading-text {
      position: absolute;
      top: 12px;
      width: 20px;
      left: 12px;
      white-space: nowrap;
      height: 100%;
      top: 0;
      display: flex;
      align-items: center;
      &:before {
        content: "";
        height: 100%;
        border: 0.5px solid #cbd5e1;
        right: -32px;
        position: absolute;
      }
    }
    input {
      padding-left: 70px !important;
    }
  }
  .input-with-right-leading-text {
    position: relative;
    .right-leading-text {
      position: absolute;
      top: 12px;
      width: auto;
      right: 0;
      white-space: nowrap;
      height: 100%;
      top: 0;
      display: flex;
      align-items: center;
      padding: 0 20px;
      text-align: center;
      font-weight: 500;
      &:before {
        content: "";
        height: 100%;
        border: 0.5px solid #cbd5e1;
        position: absolute;
        left: 0;
      }
    }
    input {
      padding-right: 70px;
    }
  }

  .ff-select-with-icon {
    select {
      padding-left: 29px;
    }

    .prefix_value {
      display: flex;
      position: absolute;
      align-items: center;
      min-width: 30px;
      min-height: 100%;
      top: 0;
      font-size: 24px;
      font-weight: 500;
      cursor: initial;
      background: none;
      text-align: center;
      justify-content: center;
      padding-left: 10px;
    }
  }

  .input_with_phone_number {
    position: relative;
    input {
      padding-left: 100px !important;
    }
    .phone_prefix {
      display: flex;
      gap: 5px;
      position: absolute;
      align-items: center;
      height: 100%;
      min-width: 80px;
      padding: 0 10px;
      box-sizing: border-box;
      color: #000;
      border-right: 1px solid #e7e7e7;
      top: 0;
      height: 45px;
      cursor: pointer;
      span.country-flag {
        font-size: 22px;
      }
    }

    .countries-list {
      .suggestion-item {
        gap: 5px;
        display: flex;
        align-items: center;
      }
    }
  }
  input:disabled ~ .prefix_value,
  input:disabled ~ .postfix_value {
  }

  input,
  textarea,
  select,
  .elaDropdown {
    display: flex;
    width: 100%;
    background: none;
    flex-direction: row;
    box-sizing: border-box;
    align-items: center;
    padding: 10px 14px;
    border: none;
    /* field */

    /* Auto layout */
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 12px;
    gap: 8px;

    height: 45px;

    background: #ffffff;
    /* Input/normal */
    box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.1),
      0px 0px 0px 1px rgba(18, 18, 18, 0.1);
    border-radius: 6px;
    border-radius: var(--radius-md, 6px);

    // border: 2px solid #DEDFE3;

    font-family: var(--font-2);
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;

    color: var(--color-text-body);

    &::placeholder {
      color: #a1a1a9;
    }

    &:focus,
    &:active {
      border-color: var(--color--primary);
      outline: var(--color--primary);

      box-shadow: 0px 0px 0px 4px rgba(18, 18, 18, 0.1),
        0px 1px 1px rgba(18, 18, 18, 0.1), 0px 0px 0px 1px rgba(18, 18, 18, 0.2),
        0px 1px 3px rgba(18, 18, 18, 0.1);
    }
    &:disabled {
      background: rgba(18, 18, 18, 0.05);
      box-shadow: none;
      ~ .prefix_value,
      ~ .postfix_value {
        background-color: #ebebeb;
      }
      cursor: not-allowed;

      &.has-border-radius {
        border-radius: 10px;
      }
      .postfix_value {
        cursor: not-allowed;
      }
      .prefix_value {
        background: #f8f8f8;
      }
      &:focus,
      &:active {
        outline: none;
        border-color: var(--color--primary-lighter);
      }
      color: #a1a1a9;
    }
  }
  select {
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    background: url("data:image/svg+xml,%3Csvg width='8' height='6' viewBox='0 0 8 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1.58789L3.6997 4.28759C3.8656 4.45349 4.1344 4.45349 4.3003 4.28759L7 1.58789' stroke='%23323232' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A")
      no-repeat 95% 50%;
    // background: url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0Ljk1IDEwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZmZjt9LmNscy0ye2ZpbGw6IzQ0NDt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPmFycm93czwvdGl0bGU+PHJlY3QgY2xhc3M9ImNscy0xIiB3aWR0aD0iNC45NSIgaGVpZ2h0PSIxMCIvPjxwb2x5Z29uIGNsYXNzPSJjbHMtMiIgcG9pbnRzPSIxLjQxIDQuNjcgMi40OCAzLjE4IDMuNTQgNC42NyAxLjQxIDQuNjciLz48cG9seWdvbiBjbGFzcz0iY2xzLTIiIHBvaW50cz0iMy41NCA1LjMzIDIuNDggNi44MiAxLjQxIDUuMzMgMy41NCA1LjMzIi8+PC9zdmc+) no-repeat 95% 50%;
    color: var(--color--text);
  }

  textarea {
    height: 100px;
    &.property_description {
      height: initial;
    }
  }
}

.checkbox_item {
  display: flex;
  align-items: center;
  gap: 10px;
  .checkbox_switch {
    position: relative;
    width: 57px;

    height: 32px;
    background: #ebebeb;
    border-radius: 50px;
    transition: 0.2s ease-in;
    cursor: pointer;
    svg {
      display: none;
    }

    &.checked {
      background: #000;
      svg {
        display: block;
        position: absolute;
        z-index: 2;
        right: 4px;
        top: 4px;
      }
      &:after {
        left: initial;
        right: 3px;
      }
    }
    &:after {
      transition: 0.2s ease-in;
      width: 26px;
      height: 26px;
      content: "";
      position: absolute;
      left: 0;
      border-radius: 50px;
      background: #fff;
      top: 3px;
      left: 3px;
    }
  }
}

.new-autocomplete-values {
  position: absolute;
  top: 60px;
  background: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 7px;
  z-index: 10;
  min-width: 100%;
  border: 1px solid #d8d8d8;
  box-shadow: none;
  max-height: 282px;
  overflow: auto;

  .autocomplete-item {
    margin-bottom: 0;
    padding: 12px 18px;
    display: flex;
    align-items: center;
    gap: 10px;
    p {
      color: #777777;
      span {
        color: #000;
        font-weight: 600;
      }
    }

    &:hover {
      background: #f1f1f1;
    }
    i {
      font-size: 26px;
    }
    cursor: pointer;
  }
}
.input-autocomplete {
  position: relative;

  .autocomplete-values {
    position: absolute;
    top: 48px;
    background: #fff;
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.084),
      0px 2px 3px rgba(0, 0, 0, 0.168);
    padding: 5px 18px;
    font-size: 15px;
    font-weight: 500;
    border-radius: 7px;
    z-index: 10;
    min-width: 100%;

    .autocomplete-item {
      margin-bottom: 5px;
      &:hover {
        color: var(--color--primary);
      }
      cursor: pointer;
    }
  }
}
#ff-drag-map {
  position: relative;
  height: 300px;
  border-radius: 13px;
  margin-top: 20px;
}

.clear_input_value {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 49px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.suggestions-dropdown {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  z-index: 50;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 240px;
  overflow-y: auto;
  margin-top: 5px;
  padding-top: 5px;
  padding-bottom: 5px;
  .search-container {
    position: sticky;
    top: 0;
    margin-bottom: 10px;
    background: #fff;
    input {
      padding-left: 10px !important;
      border: none;
      box-shadow: none;
      background: #f5f5f5;
      height: 34px;
      margin-bottom: 5px;
      &:focus{
        box-shadow: none;
        
      }
    }
  }
  .suggestion-item {
    margin: 0.5rem;
    padding: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 0.5rem;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 400;
    color: #111827;
    margin-bottom: 0;
    margin-top: 0;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background-color: #f1f5f9;
      color: #1e293b;
    }
    &.selected{
       background-color: #f1f5f9;
      color: #1e293b;
    }
  }
}
