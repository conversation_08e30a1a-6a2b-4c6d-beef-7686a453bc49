@use "./mixin" as *;
@include margin-classes(20);

.shadow-one {
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.084), 0px 2px 3px rgba(0, 0, 0, 0.168);
}
.relative {
  position: relative;
}
.w-full {
  width: 100%;
}

.b-rounded {
  border-radius: 15px;
}
.has-border-bottom {
  border-bottom: 1px solid #dedfe3;
}
.text-center {
  text-align: center;
}

.ff-flex {
  display: flex;
}
.ff-column {
  flex-direction: column;
}
.ff-row {
  flex-direction: row;
}
.ff-space-between {
  justify-content: space-between;
}
.ff-justify-center {
  justify-content: center;
}
.ff-justify-end {
  justify-content: flex-end;
}
.ff-flex-align-center {
  align-items: center;
  i {
    line-height: initial;
  }
}
.ff-gap-5 {
  gap: 5px;
}
.ff-gap-10 {
  gap: 10px;
}
.ff-gap-24 {
  gap: 24px;
}

.mt-0-5 {
  margin-top: 5px;
}
.text-primary {
  color: var(--color--primary);
}

.reset-default-button {
  background: none;
  button {
    padding: 0;
    min-width: initial;
    background: none;
    border: none;
    margin: 0;
    font-size: 14px;
    height: initial;
    &:focus,
    &:active {
      outline: none;
      background: none;
      box-shadow: none;
    }
    &.btn-secondary {
      background: none;
      &:focus,
      &:active {
        outline: none;
        background: none;
        box-shadow: none;
      }
    }

    &:hover {
      background: none;
    }
  }
}

.pointer {
  cursor: pointer;
}

.slide-fade-up-enter-active {
  transition: all 0.25s ease-in-out;
  transition-delay: 0.1s;
  position: relative;
}
.slide-fade-up-leave-active {
  transition: all 0.25s ease-in-out;
  position: absolute;
}
.slide-fade-up-enter {
  opacity: 0;
  transform: translateY(15px);
  pointer-events: none;
}
.slide-fade-up-leave-to {
  opacity: 0;
  transform: translateY(-15px);
  pointer-events: none;
}

.has-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@include respond-below(sm) {
  .hide_sm {
    display: none;
  }
  .res-mt-2 {
    margin-top: 20px;
  }
  .res-mb-2 {
    margin-bottom: 20px;
  }
  .res_footer_col_item {
    margin-top: 20px;
  }
  .res_footer_social_icon {
    svg {
      margin-top: 10px;
      width: 40px;
    }
  }
}
@include respond-below(xs) {
  .hide_xs {
    display: none;
  }
}
