@use "../../assets/scss/mixin" as *;
.btn {
  box-sizing: border-box;

  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 14px;
  gap: 8px;
  border: none;
  cursor: pointer;
  width: auto;
  height: 36px;

  background: radial-gradient(
      100% 100% at 50% 0%,
      rgba(255, 255, 255, 0.3) 0%,
      rgb(0 0 0 / 68%) 100%
    ),
    #09090b;
  box-shadow: 0px 0px 0px 1px #121212;
  border-radius: 8px;

  flex: none;
  order: 3;
  align-self: stretch;
  flex-grow: 0;
  transition: background 0.3s ease, box-shadow 0.2s ease, transform 0.2s ease;
  font-family: var(--font-1);
  font-weight: 500;
  svg {
    /* icn-zap */
    width: 16px;
    height: 16px;
  }

  font-style: normal;
  font-size: 14px;
  line-height: 20px;

  color: #ffffff;

  &:hover {
    /* Button/primary-hover */
    background: radial-gradient(
        100% 100% at 50% 0%,
        rgba(255, 255, 255, 0.36) 0%,
        rgba(255, 255, 255, 0) 100%
      ),
      #18181b;
    /* Button/Primary/hover */
    box-shadow: 0px 0px 0px 1px #121212;
  }
  &:active {
    /* Button/primary-pressed */
    background: radial-gradient(
        100% 100% at 50% 0%,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0) 53.12%
      ),
      #121212;
    /* Button/Primary/pressed */
    box-shadow: 0px 0px 0px 1px #121212;
  }
  &:focus {
    /* Button/primary-focused */
    background: radial-gradient(
        100% 100% at 50% 0%,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0) 100%
      ),
      #09090b;
    /* Button/Primary/focus */
    box-shadow: 0px 0px 0px 4px rgba(18, 18, 18, 0.2), 0px 0px 0px 1px #121212;
  }
  &:disabled {
    .ff-custom-icon {
      filter: grayscale(1);
      opacity: 0.4;
    }
  }

  &[disabled] {
    /* Type=Primary, State=Disabled, Size=lg */
    background: none;
    /* Button/Primary/disabled */
    box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.05),
      0px 0px 0px 1px rgba(18, 18, 18, 0.04), 0px 1px 3px rgba(18, 18, 18, 0.05);
    border-radius: 8px;
    pointer-events: none;
    color: rgba(18, 18, 18, 0.4);
  }

  &.outline {
  }
  &.btn-large {
    height: 36px;
    &.btn--box {
      width: 36px;
    }
    @include respond-below(sm) {
      height: 46px;
    }
  }
  &.btn-medium {
    height: 32px;
    padding: 6px 12px;

    &.btn--box {
      width: 32px;
    }
  }
  &.btn-small {
    height: 24px;
    font-size: 12px;
    line-height: 16px;
    padding: 4px 6px;
    gap: 4px;
    border-radius: 6px;
    &.btn--box {
      width: 24px;
    }
  }
  &.btn--box {
    padding: 0;
    span {
      display: none;
    }
  }
  &.fullwidth {
    width: 100%;
  }
  &.btn--primary {
    .loader.loader--primary {
      border-left: 1.1em solid #fff;
    }
  }
  &.btn--secondary {
    background: #ffffff;
    /* Button/Secondary/normal */
    box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.1),
      0px 0px 0px 1px rgba(18, 18, 18, 0.07), 0px 1px 3px rgba(18, 18, 18, 0.1);
    color: #09090b;
    &:hover {
      background: #fafafa;
      /* Button/Secondary/hover */
      box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.2),
        0px 0px 0px 1px rgba(18, 18, 18, 0.1), 0px 1px 3px rgba(18, 18, 18, 0.1);
    }
    &:hover {
      background: #fafafa;
      /* Button/Secondary/hover */
      box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.2),
        0px 0px 0px 1px rgba(18, 18, 18, 0.1), 0px 1px 3px rgba(18, 18, 18, 0.1);
    }
    &:active {
      /* Type=Secondary, State=Pressed, Size=sm */

      background: #f4f4f5;
      /* Button/Secondary/pressed */
      box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.1),
        0px 0px 0px 1px rgba(18, 18, 18, 0.07),
        0px 1px 3px rgba(18, 18, 18, 0.1);
    }
    &:focus {
      background: #ffffff;
      /* Button/Secondary/focus */
      box-shadow: 0px 0px 0px 4px rgba(18, 18, 18, 0.1),
        0px 1px 1px rgba(18, 18, 18, 0.1),
        0px 0px 0px 1px rgba(18, 18, 18, 0.07),
        0px 1px 3px rgba(18, 18, 18, 0.1);
    }
    &:disabled {
      pointer-events: none;
      background: #ffffff;
      /* Button/Secondary/disabled */
      box-shadow: 0px 1px 1px rgba(18, 18, 18, 0.05),
        0px 0px 0px 1px rgba(18, 18, 18, 0.04),
        0px 1px 3px rgba(18, 18, 18, 0.05);
      color: rgba(18, 18, 18, 0.4);
    }
  }
  &.btn--flat {
    background: none;
    border: none;
    box-shadow: none;
    padding: 0px;
    gap: 4px;
    color: #09090b;
    text-decoration: underline;
    text-underline-offset: 4px;
    &:hover {
      background: rgba(18, 18, 18, 0.05);
    }
    &:disabled {
      pointer-events: none;
      background: #ffffff;
      /* Type=Disabled, Size=md, Weight=Medium */
      color: rgba(18, 18, 18, 0.3);
    }
  }
}

.ff-button-dropdown {
  background: #fff;
  .dropdown-button {
    border-radius: 4px;
    p {
      color: var(--color--text);
      span {
        color: #000;
      }
    }
    &.dropdown-bordered {
      background: #fff;
      border: 1px solid #e2e2e2;
      color: #000;
      p {
        font-weight: 400;
        display: flex;
        gap: 5px;
        span {
          font-weight: 600;
        }
      }
    }
    &.dropdown--large {
      padding: 16px 24px;
      height: 56px;
      font-weight: 800;
    }
    &.dropdown--medium {
      padding: 10px 24px;
      height: 41px;

      font-size: 14px;
      border-radius: 7px;
    }
    &.dropdown--small {
      padding: 10px;
      height: 30px;

      border-radius: 4px;
      p {
        font-size: 12px;
      }
    }
    &.dropdown--white {
      &:focus {
        background: red;
      }
    }
  }
}
