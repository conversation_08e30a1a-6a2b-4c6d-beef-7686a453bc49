.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.8);
}
.modal_manual_padding{
  padding:0 22px;

  .section_with_checks{
    padding:0;
  }
  &.section_with_seperator{
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 19px;
  }
}
.modal {
  .modal-content {
    box-shadow: none;
    border: none;
  }
  .modal-dialog {
    border: none;
  }

  
  .without-footer {
    padding-bottom: 38px;
  }

  .immofuchs-small-modal {
    max-width: 220px;
    padding: 28px;
  }

  .immofuchs-medium-modal {
    max-width: 760px;
  }
  .immofuchs-default-modal {
    max-width: 560px;
  }

  .immofuchs-large-modal {
    max-width: 960px;
  }

  .immofuchs-v-large-modal {
    max-width: 1070px;
  }

  .immofuchs-modal-title {
    width: 100%;
    display: flex;
    border: none;
    padding: 20px;
    .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        h5{
            margin:0;
            font-weight:500;
        }
    }
    
  }

  .modal-body-desc {
    padding: 0 28px;

    
  }

  .modal_actions {
    border:none;
    margin-top: 30px;
    display: flex;
    padding: 10px 36px;
    justify-content: flex-end;
    background: #f8f7fb;
  }
}


@media only screen and (max-width: 650px) {
  .modal {
   

    .modal_actions {
        
    }
  }
}

@media only screen and (max-width: 649px) {
  .modal {
    

    .modal-body-desc {
      padding: 0 25px;
    }

    .immofuchs-small-modal {
      max-width: initial;
      width: 100%;
      padding: 14px;
    }
  }
}
