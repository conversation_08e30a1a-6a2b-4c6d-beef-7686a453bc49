// @import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined');
@use "./variables" as *;
@use "./mixin" as *;

@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-1);
  margin: 0;
  color: #09090b;
}
h1 {
  font-size: 44px;
  line-height: 52px;
  letter-spacing: -0.03em;
  @include respond-below(xs) {
    font-size: 36px;
    line-height: 44px;
  }
  @include respond-below(sm) {
    font-size: 44px;
    line-height: 52px;
  }
}
h2 {
  font-size: 40px;
  line-height: 48px;
  letter-spacing: -0.03em;
  @include respond-below(xs) {
    font-size: 32px;
    line-height: 40px;
  }
  @include respond-below(sm) {
    font-size: 40px;
    line-height: 48px;
  }
}
h3 {
  font-family: var(--font-2);
  font-size: 36px;
  line-height: 44px;
  &.bold {
    font-weight: 700;
  }
  &.small {
    font-size: 36px;
    line-height: 40px;
    font-weight: 500;
    letter-spacing: 1px;
  }
}
h4 {
  font-size: 32px;
  font-family: var(--font-3);
  line-height: 40px;
  font-weight: 700;
}
h5 {
  font-size: 24px;
  font-family: var(--font-2);
  line-height: 32px;
}
h6 {
  font-size: 20px;
  font-family: var(--font-2);
  line-height: 28px;
  &.smaller {
    font-size: 18px;
    line-height: 24px;
  }
  &.helper {
    box-sizing: border-box;
    min-width: 0px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1.2px;
    line-height: 16px;
    align-self: start;
    margin-top: 12px;
    margin-bottom: 8px;
    padding-top: 2px;
    padding-bottom: 2px;
    &.no-space {
      margin: 0;
      padding: 0;
    }
  }
}
body {
  text-rendering: optimizeLegibility;
  font-family: var(--font-2);
}
a,
p {
  font-family: var(--font-1);
  font-weight: 500;
  margin: 0;
  font-size: 14px;
  line-height: 20px;
  &.lead {
    font-size: 18px;
    line-height: 24px;
  }
}
a {
  text-decoration: none;
  &:hover {
    color: var(--color--primary);
  }
  &.hover-primary {
    &:hover {
      color: var(--color--primary-darker);
    }
  }
}

p {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  &.lead {
    font-size: 1.25rem;
    font-weight: 300;
    a {
      font-size: 1.25rem;
      font-weight: 300;
    }
    &.overline {
      font-size: 12px;
      line-height: 16px;
      font-weight: 500;
      letter-spacing: 1.2px;
      text-transform: uppercase;
    }
  }
  &.bigger {
    font-size: 18px;
    line-height: 25px;
  }
  &.smaller {
    font-size: 14px;
    line-height: 120%;
    a {
      font-size: 14px;
      line-height: 120%;
    }
  }
  &.v-small {
    font-size: 12px;
    line-height: 120%;
  }
}
.text-bold {
  font-weight: 600;
}
.text-medium {
  font-weight: 500;
}
.text-regular {
  font-weight: 400 !important;
}
.stroke-light {
  font-weight: 200;
}
.text-compress {
  letter-spacing: -0.04em;
}
a {
  font-size: 16px;
  font-weight: 500;
  &:hover {
    text-decoration: none;
  }
  &.smaller {
    font-size: 14px;
    line-height: 120%;
    a {
      font-size: 14px;
      line-height: 120%;
    }
  }
}
