.ff-popover {
  position: absolute;
  background: #0C111D;
  
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 12px;
  border-radius: 8px;
  display: none; // Initially hidden
  z-index: 1000;
 
  font-weight: 500;
  min-width: 260px;
  line-height: 20px; 
 
  z-index: 1000;
  width: max-content;
  max-width: 260px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);

  .popover-title {
    font-size: 16px;
    color: #fff;
    font-weight: bold;
    font-family: var(--font-2);
    margin-bottom: 10px;
  }

  .popover-content {
    margin-bottom: 15px;
    font-size: 16px;
    color: #D0D5DD;
    font-family: var(--font-2);
  }

  .popover-actions {
    display: flex;
    justify-content: flex-end;

    button {
      margin-left: 10px;
      padding: 5px 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }

      // Specific styles for Cancel and Confirm buttons
      &.cancel-button {
        background-color: #f44336; // Red color for cancel
        color: white;
      }

      &.confirm-button {
        background-color: #4caf50; // Green color for confirm
        color: white;
      }
    }
  }
}
