// TooltipStyles.scss
.ff-tooltip {
  position: absolute;
  background: #0C111D;
  color: #D0D5DD;
  padding: 12px;
  border-radius: 8px;
  display: none;
  font-size: 16px;
  font-weight: 500;
  min-width: 260px;
  line-height: 20px; 
  font-family: var(--font-2);
  z-index: 1000;
  width: max-content;
  max-width: 260px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
  // Common styles for arrows
    &::before {
        content: '';
        position: absolute;
        border-style: solid;
        width: 0;
        height: 0;
    }

  &.top {
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      &::before {
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-width: 5px 5px 0 5px;
        border-color: black transparent transparent transparent;
    }
  }

  &.bottom {
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      &::before {
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-width: 0 5px 5px 5px;
        border-color: transparent transparent black transparent;
    }
  }

  &.left {
      right: 100%;
      top: 50%;
      transform: translateY(-50%);
      &::before {
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        border-width: 5px 0 5px 5px;
        border-color: transparent transparent transparent black;
    }
  }

  &.right {
      left: 100%;
      top: 50%;
      transform: translateY(-50%);
      &::before {
        right: 100%;
        top: 50%;
        transform: translateY(-50%);
        border-width: 5px 5px 5px 0;
        border-color: transparent black transparent transparent;
    }
  }
}
