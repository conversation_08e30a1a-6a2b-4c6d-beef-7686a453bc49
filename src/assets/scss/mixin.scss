@use "sass:map";

@mixin margin-classes($n) {
  @for $i from 1 through $n {
    .mb-#{$i} {
      margin-bottom: $i * 10px !important;
    }
    .mt-#{$i} {
      margin-top: $i * 10px !important;
    }
    .ml-#{$i} {
      margin-left: $i * 10px !important;
    }
    .mr-#{$i} {
      margin-right: $i * 10px !important;
    }
  }
}
@mixin gap-classes($n) {
  @for $i from 1 through $n {
    .gap-#{$i} {
      gap: $i * 10px !important; // Use $i instead of $ipx
    }
  }
}

//   .space-top-1{

//   }
@mixin spacing-classes($n) {
  @for $i from 1 through $n {
    .space-top-#{$i} {
      padding-top: $i * 10px;
    }
    .space-bottom-#{$i} {
      padding-bottom: $i * 10px;
    }
    .space-left-#{$i} {
      padding-left: $i * 10px;
    }
    .space-right-#{$i} {
      padding-right: $i * 10px;
    }
  }
}

@mixin height-classes($n) {
  @for $i from 1 through $n {
    .height-#{$i} {
      height: $i * 10px;
    }
  }
}

@mixin absolute-classes($n) {
  @for $i from 1 through $n {
    .left-#{$i} {
      left: $i * 10px;
    }
    .top-#{$i} {
      top: $i * 10px;
    }
    .bottom-#{$i} {
      bottom: $i * 10px;
    }
    .right-#{$i} {
      right: $i * 10px;
    }
  }
}

@mixin negative-absolute-classes($n) {
  @for $i from 1 through $n {
    .left--#{$i} {
      left: $i * -10px;
    }
    .top--#{$i} {
      top: $i * -10px;
    }
    .bottom--#{$i} {
      bottom: $i * -10px;
    }
    .right--#{$i} {
      right: $i * -10px;
    }
  }
}

@mixin zindex-classes($n) {
  @for $i from 1 through $n {
    .z-index-#{$i} {
      z-index: $i * 10;
    }
    .z-index--#{$i} {
      z-index: $i * -10;
    }
  }
}

//
//  MEDIA QUERIES
//––––––––––––––––––––––––––––––––––––––––––––––––––
$breakpoints: (
  xxs: 321px,
  xs: 576px,
  sm: 886px,
  md: 992px,
  lg: 1200px,
);
//
//  RESPOND ABOVE
//––––––––––––––––––––––––––––––––––––––––––––––––––
// @include respond-above(sm) {}
@mixin respond-above($breakpoint) {
  // If the breakpoint exists in the map.
  @if map.has-key($breakpoints, $breakpoint) {
    // Get the breakpoint value.
    $breakpoint-value: map.get($breakpoints, $breakpoint);
    // Write the media query.
    @media (min-width: $breakpoint-value) {
      @content;
    }
    // If the breakpoint doesn't exist in the map.
  } @else {
    // Log a warning.
  }
}
//
//  RESPOND BELOW
//––––––––––––––––––––––––––––––––––––––––––––––––––
// @include respond-below(sm) {}
@mixin respond-below($breakpoint) {
  // If the breakpoint exists in the map.
  @if map.has-key($breakpoints, $breakpoint) {
    // Get the breakpoint value.
    $breakpoint-value: map.get($breakpoints, $breakpoint);
    // Write the media query.
    @media (max-width: ($breakpoint-value - 1)) {
      @content;
    }
    // If the breakpoint doesn't exist in the map.
  } @else {
    // Log a warning.
  }
}
//
//  RESPOND BETWEEN
//––––––––––––––––––––––––––––––––––––––––––––––––––
// @include respond-between(sm, md) {}
@mixin respond-between($lower, $upper) {
  // If both the lower and upper breakpoints exist in the map.
  @if map.has-key($breakpoints, $lower) and map.has-key($breakpoints, $upper) {
    // Get the lower and upper breakpoints.
    $lower-breakpoint: map.get($breakpoints, $lower);
    $upper-breakpoint: map.get($breakpoints, $upper);
    // Write the media query.
    @media (min-width: $lower-breakpoint) and (max-width: ($upper-breakpoint - 1)) {
      @content;
    }
    // If one or both of the breakpoints don't exist.
  } @else {
    // If lower breakpoint is invalid.
    @if (map.has-key($breakpoints, $lower) == false) {
      // Log a warning.
    }
    // If upper breakpoint is invalid.
    @if (map.has-key($breakpoints, $upper) == false) {
      // Log a warning.
    }
  }
}

@mixin no-sb() {
  &::-webkit-scrollbar {
    height: 0;
    width: 0;
  }

  &::-webkit-scrollbar-thumb {
    height: 0;
    width: 0;
  }

  &::-webkit-scrollbar-track {
    height: 0;
    width: 0;
  }
}
