// import { api } from "@/lib/server";

// import type {
//   ICreateAgent,
//   Workflow,
//   Agent,
//   WorkflowUpdateI,
//   AgentWidget,
//   AgentType,
//   ChatbotAnalytics,
//   ChatbotConversations,
//   ConversationMessagesResponse,
//   ConversationMessage,
// } from "@/stores/types/agent.types";

// import type { Tool } from "@/stores/types/tool.types";
// import type { ApiResponse } from "@types/general.types";

// import { ErrorHandler } from "@/lib/errorHandler";
// import { useAgentStore } from "@/stores/agent.store";
// import type { LlmModel, ModelConfig } from "@/stores/types/llm.types";

// export class AgentService {
//   private static readonly APP_BASE_PATH = "/api/user";
//   private static readonly API_BASE_PATH = "/api/user/chatbots";

//   static async createAgent(
//     agentData: ICreateAgent
//   ): Promise<ApiResponse<Agent>> {
//     try {
//       const { data } = await api.post<ApiResponse<Agent>>(
//         `${this.API_BASE_PATH}/agent/create`,
//         agentData
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::createAgent", error);
//     }
//   }

//   static async fetchAgentById(agentID: string): Promise<ApiResponse<Agent>> {
//     try {
//       const { data } = await api.get<ApiResponse<Agent>>(
//         `${this.API_BASE_PATH}/${agentID}`
//       );
//       const agentStore = useAgentStore();
//       agentStore.setAgent(data.data);
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::fetchAgentById", error);
//     }
//   }

//   static async fetchAgents(): Promise<ApiResponse<Agent[]>> {
//     try {
//       const { data } = await api.get<ApiResponse<Agent[]>>(
//         `${this.APP_BASE_PATH}/chatbots`
//       );
//       const agentStore = useAgentStore();
//       agentStore.setAgents(data.data);
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgents", error);
//     }
//   }

//   static async fetchAgentTypes(): Promise<ApiResponse<AgentType[]>> {
//     try {
//       const { data } = await api.get<ApiResponse<AgentType[]>>(
//         `${this.APP_BASE_PATH}/agent-types`
//       );
//       const agentStore = useAgentStore();
//       agentStore.setAgentTypes(data.data);
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgents", error);
//     }
//   }

//   static async getAgentWorkflow(
//     chatbotId: string
//   ): Promise<ApiResponse<Workflow>> {
//     try {
//       const { data } = await api.get<ApiResponse<Workflow>>(
//         `${this.API_BASE_PATH}/${chatbotId}/workflow`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentWorkflow", error);
//     }
//   }
//   static async createAgentWorkflow(
//     workflowData: Workflow
//   ): Promise<ApiResponse<Workflow>> {
//     try {
//       const { data } = await api.post<ApiResponse<Workflow>>(
//         `${this.API_BASE_PATH}/workflow/${workflowData.agentID}`,
//         workflowData
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::createAgentWorkflow", error);
//     }
//   }
//   static async updateWorkflowNode(
//     workflowData: WorkflowUpdateI
//   ): Promise<ApiResponse<Workflow>> {
//     try {
//       const { data } = await api.put<ApiResponse<Workflow>>(
//         `${this.API_BASE_PATH}/workflow/${workflowData.workflowId}/node/${workflowData.nodeId}`,
//         workflowData
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::updateWorkflowNode", error);
//     }
//   }
//   static async saveAgentWorkflow(
//     workflowData: Workflow
//   ): Promise<ApiResponse<Workflow>> {
//     try {
//       const { data } = await api.put<ApiResponse<Workflow>>(
//         `${this.API_BASE_PATH}/workflow/${workflowData.agentID}/${workflowData.agentID}`,
//         workflowData
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::saveAgentWorkflow", error);
//     }
//   }

//   static async getWidgetConfig(
//     chatbotId: string
//   ): Promise<ApiResponse<AgentWidget>> {
//     try {
//       const { data } = await api.get<ApiResponse<AgentWidget>>(
//         `${this.API_BASE_PATH}/${chatbotId}/widget`
//       );
//       const store = useAgentStore();

//       store.updateWidgetConfig(data.data);
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getWidgetConfig", error);
//     }
//   }

//   static async updateWidgetConfig(
//     chatbotId: string,
//     config: AgentWidget
//   ): Promise<ApiResponse<AgentWidget>> {
//     try {
//       const { data } = await api.put<ApiResponse<AgentWidget>>(
//         `${this.API_BASE_PATH}/${chatbotId}/widget`,
//         { ...config }
//       );
//       const store = useAgentStore();
//       store.updateWidgetConfig(data.data);
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::updateWidgetConfig", error);
//     }
//   }

//   static async getAgentKnowledgeBase(
//     chatbotId: string
//   ): Promise<ApiResponse<AgentWidget>> {
//     try {
//       const { data } = await api.get<ApiResponse<AgentWidget>>(
//         `${this.API_BASE_PATH}/${chatbotId}/knowledge-base`
//       );
//       const store = useAgentStore();
//       store.updateWidgetConfig(data.data);
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentKnowledgeBase", error);
//     }
//   }
//   static async saveAgentKnowledgeBase(
//     chatbotId: string,
//     documentIds: string[]
//   ): Promise<ApiResponse<AgentWidget>> {
//     try {
//       const { data } = await api.post<ApiResponse<AgentWidget>>(
//         `${this.API_BASE_PATH}/${chatbotId}/save-knowledge-base`,
//         { documentIDs: documentIds }
//       );
//       const store = useAgentStore();
//       store.updateWidgetConfig(data.data);
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::saveAgentKnowledgeBase", error);
//     }
//   }

//   static async getPromptConfiguration(
//     chatbotId: string
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.get<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/prompts`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getPromptConfiguration", error);
//     }
//   }

//   static async updatePromptConfiguration(
//     chatbotId: string,
//     promptConfig: any
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.put<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/prompts`,
//         promptConfig
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::updatePromptConfiguration", error);
//     }
//   }

//   static async getAgentEnabledTools(
//     chatbotId: string
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.get<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/tools`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentEnabledTools", error);
//     }
//   }

//   static async getAgentEnabledToolsDetails(
//     chatbotId: string,
//     toolId: string
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.get<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/tools/${toolId}`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentEnabledTools", error);
//     }
//   }

//   static async getTools(): Promise<ApiResponse<Tool>> {
//     // Update return type to ApiResponse<Tool>
//     try {
//       const { data } = await api.get<ApiResponse<Tool>>(
//         `${this.APP_BASE_PATH}/tools`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::createAgent", error);
//     }
//   }

//   static async getAgentToolConfig(
//     chatbotId: string,
//     toolId: string
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.get<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/tools/${toolId}`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentToolsConfig", error);
//     }
//   }
//   static async createAgentTool(
//     chatbotId: string,
//     config: any
//   ): Promise<ApiResponse<any>> {
//     try {
//       // Extract the name from config or use a default if not provided
//       const toolName = config.name || "product_search";

//       // Remove the name from the config object to avoid duplication
//       const { name, ...configWithoutName } = config;

//       const { data } = await api.post<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/tools`,
//         {
//           name: toolName,
//           config: configWithoutName,
//         }
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentToolsConfig", error);
//     }
//   }

//   static async updateAgentTool(
//     chatbotId: string, // SNOWID
//     toolId: string,
//     config: any
//   ): Promise<ApiResponse<any>> {
//     try {
//       // Extract the name from config or use a default if not provided

//       // Remove the name from the config object to avoid duplication
//       const { name, ...configWithoutName } = config;

//       const { data } = await api.put<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/tools/${toolId}`,
//         {
//           config: configWithoutName,
//         }
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::updateAgentTool", error);
//       // Make sure to rethrow the error or return a rejected promise
//     }
//   }

//   static async disableTool(
//     chatbotId: string,
//     toolId: string
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.delete<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/tools/${toolId}`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::disableTool", error);
//     }
//   }

//   // src/services/AgentService.ts

//   /**
//    * Test a product search tool configuration without saving it
//    * @param chatbotId The ID of the chatbot
//    * @param config The configuration to test, or toolId of an existing tool
//    */
//   static async testProductSearchTool(
//     chatbotId: string,
//     options: { toolId?: string; config?: Record<string, any> }
//   ): Promise<ApiResponse<any>> {
//     try {
//       // Ensure at least one of toolId or config is provided
//       if (!options.toolId && !options.config) {
//         throw new Error("Either toolId or config must be provided");
//       }

//       const { data } = await api.post<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/tools/test`,
//         options
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::testProductSearchTool", error);
//     }
//   }

//   // GET CHATBOT LLM MODELS

//   static async getAgentLLMModel(
//     chatbotId: string
//   ): Promise<ApiResponse<LlmModel>> {
//     try {
//       const { data } = await api.get<ApiResponse<LlmModel>>(
//         `${this.API_BASE_PATH}/${chatbotId}/model`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentLLMModel", error);
//     }
//   }

//   static async updateAgentModel(
//     modelData: ModelConfig
//   ): Promise<ApiResponse<LlmModel>> {
//     try {
//       const { data } = await api.put<ApiResponse<LlmModel>>(
//         `${this.API_BASE_PATH}/${modelData.chatbotID}/model`,
//         modelData
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::updateAgentModel", error);
//     }
//   }

//   static async getAgentTokenAnalytics(
//     chatbotId: string
//   ): Promise<ApiResponse<ChatbotAnalytics>> {
//     try {
//       const { data } = await api.get<ApiResponse<ChatbotAnalytics>>(
//         `${this.API_BASE_PATH}/${chatbotId}/token-analytics`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentTokenAnalytics", error);
//     }
//   }
//   static async getChatbotConversations(
//     chatbotId: string
//   ): Promise<ApiResponse<ChatbotConversations>> {
//     try {
//       const { data } = await api.get<ApiResponse<ChatbotConversations>>(
//         `${this.API_BASE_PATH}/${chatbotId}/conversations`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentTokenAnalytics", error);
//     }
//   }
//   static async getChatbotConversationItem(
//     chatbotId: string,
//     conversationId: string,
//     page: number = 1
//   ): Promise<ApiResponse<ConversationMessagesResponse>> {
//     try {
//       const { data } = await api.get<ApiResponse<ConversationMessagesResponse>>(
//         `${this.API_BASE_PATH}/${chatbotId}/conversations/${conversationId}/messages?page=${page}`
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::getAgentTokenAnalytics", error);
//     }
//   }

//   static async updateAgentStatus(
//     chatbotId: string,
//     status: boolean
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.put<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/status`,
//         { status }
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::updateAgentStatus", error);
//     }
//   }

//   static async updateAgentDomain(
//     chatbotId: string,
//     domainId: string
//   ): Promise<ApiResponse<any>> {
//     try {
//       const { data } = await api.put<ApiResponse<any>>(
//         `${this.API_BASE_PATH}/${chatbotId}/domain`,
//         { domainId }
//       );
//       return data;
//     } catch (error) {
//       ErrorHandler.handle("AgentService::updateAgentDomain", error);
//     }
//   }
// }
