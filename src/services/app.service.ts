import { api } from "@/lib/server";

import type { Job, JobSearchQuery } from "@/types/job.types";
import type { Place, PlaceSearchQuery } from "@/types/place.types";
import type { ApiResponse } from "@/types/general.types";
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from "@/lib/errorHandler";
export class AppService {
  private static readonly API_BASE_PATH = "/api/user";

  static async searchJobPosition(
    searchQuery: JobSearchQuery
  ): Promise<ApiResponse<Job>> {
    try {
      const { data } = await api.get<ApiResponse<Job>>(
        `${this.API_BASE_PATH}/app/search?q=${searchQuery.q}&type=${searchQuery.type}`
      );
      return data;
    } catch (error) {
      ErrorHandler.handle("AppService::searchJobPosition", error);
    }
  }

  static async searchPlace(
    searchQuery: PlaceSearchQuery
  ): Promise<ApiResponse<Place>> {
    try {
      const { data } = await api.get<ApiResponse<Place>>(
        `${this.API_BASE_PATH}/app/search?q=${searchQuery.q}&type=${searchQuery.type}`
      );
      return data;
    } catch (error) {
      ErrorHandler.handle("AppService::searchPlace", error);
    }
  }

}
