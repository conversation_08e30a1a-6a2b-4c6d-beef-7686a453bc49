import type {
  WebSocketChannel,
  WebSocketConfig,
  WebSocketMessage,
  StoreChannelParams,
} from "@/stores/types/websocket.types";

import { useWebSocketStore } from "@/stores/websocket.store";
import { useAuthStore } from "@/stores/auth.store";

export class WebSocketService {
  private static _defaultInstance?: WebSocketService;

  private connections: Map<string, WebSocket> = new Map();
  private reconnectTimeouts: Map<string, number> = new Map();
  private connectionAttempts: Map<string, boolean> = new Map(); // Track connection attempts
  private store = useWebSocketStore();
  private authStore = useAuthStore();

  private config: WebSocketConfig;

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      reconnectAttempts: 5,
      reconnectInterval: 5000,
      heartbeatInterval: 30000,
      baseUrl: import.meta.env.VITE_WS_URL,
      ...config,
    };
  }

  public static getDefaultInstance(): WebSocketService {
    return (this._defaultInstance ??= new WebSocketService());
  }

  private getConnectionKey(
    channel: WebSocketChannel,
    storeId?: string
  ): string {
    return channel === "store" && storeId
      ? `${channel}-${storeId}`
      : channel;
  }

  public async connectToChannel(
    channel: WebSocketChannel,
    params: Partial<StoreChannelParams> = {}
  ): Promise<void> {
    if (!this.authStore.accessToken) {
      throw new Error("No authentication token found");
    }

    const connectionKey = this.getConnectionKey(channel, params.storeId);
    
    // Check if we're already connected or in the process of connecting
    const existingConnection = this.connections.get(connectionKey);
    const isConnecting = this.connectionAttempts.get(connectionKey);
    
    // Skip if already connecting or open
    if (isConnecting) {
      console.log(`Connection attempt already in progress for ${connectionKey}`);
      return;
    }
    
    if (
      existingConnection?.readyState === WebSocket.CONNECTING ||
      existingConnection?.readyState === WebSocket.OPEN
    ) {
      console.log(`Connection already exists for ${connectionKey}`);
      return;
    }

    // Disconnect existing connection if needed
    if (existingConnection) {
      this.disconnectChannel(channel, params.storeId);
    }

    try {
      // Mark that we're attempting to connect
      this.connectionAttempts.set(connectionKey, true);
      
      // Format URL parameters in the correct order with storeId first if present
      let url = `${this.config.baseUrl}/${channel}`;
      
      if (params.storeId) {
        url += `?storeId=${params.storeId}&token=${this.authStore.accessToken}`;
      } else {
        url += `?token=${this.authStore.accessToken}`;
      }
      
      // Add any additional parameters
      Object.entries(params).forEach(([key, value]) => {
        if (key !== 'storeId' && value !== undefined) {
          url += `&${key}=${value}`;
        }
      });
      
      
      const ws = new WebSocket(url);
      this.setupWebSocketHandlers(ws, channel, params.storeId);
      this.connections.set(connectionKey, ws);

      await this.waitForConnection(ws);
    } catch (error) {
      console.error(
        `Failed to create WebSocket connection for ${connectionKey}:`,
        error
      );
      this.handleConnectionError(channel, params.storeId);
      throw error;
    } finally {
      // Clear the connection attempt flag regardless of success or failure
      this.connectionAttempts.delete(connectionKey);
    }
  }

  private waitForConnection(ws: WebSocket): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("Connection timeout"));
      }, 10000);

      const cleanup = () => clearTimeout(timeout);

      ws.addEventListener(
        "open",
        () => {
          cleanup();
          resolve();
        },
        { once: true }
      );

      ws.addEventListener(
        "error",
        (error) => {
          cleanup();
          reject(error);
        },
        { once: true }
      );
    });
  }

  private setupWebSocketHandlers(
    ws: WebSocket,
    channel: WebSocketChannel,
    storeId?: string
  ): void {
    const connectionKey = this.getConnectionKey(channel, storeId);

    ws.onopen = () => {
      console.log(`WebSocket connected: ${connectionKey}`);
      this.store.setConnectionStatus(
        channel,
        { isConnected: true, reconnectAttempt: 0 },
        storeId
      );
      this.clearReconnectTimeout(connectionKey);
    };

    ws.onmessage = (event: MessageEvent) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        message.channel = channel;
        message.timestamp = Date.now();
        if (storeId) message.storeId = storeId;
        this.store.addMessage(channel, message);
      } catch (error) {
        console.error("Failed to parse WebSocket message:", error);
      }
    };

    ws.onclose = (event) => {
      console.log(
        `WebSocket closed: ${connectionKey}`,
        event.code,
        event.reason
      );
      this.handleConnectionError(channel, storeId);
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      this.handleConnectionError(channel, storeId);
    };
  }

  private handleConnectionError(
    channel: WebSocketChannel,
    storeId?: string
  ): void {
    const connectionKey = this.getConnectionKey(channel, storeId);
    const currentStatus = storeId
      ? this.store.getStoreConnectionStatus(storeId)
      : this.store.getChannelConnectionStatus(channel);

    const reconnectAttempt = (currentStatus?.reconnectAttempt || 0) + 1;
    this.store.setConnectionStatus(
      channel,
      {
        isConnected: false,
        reconnectAttempt,
        error: `Connection lost. Attempt ${reconnectAttempt}/${this.config.reconnectAttempts}`,
      },
      storeId
    );

    if (reconnectAttempt <= this.config.reconnectAttempts) {
      const timeout = window.setTimeout(() => {
        console.log(`Attempting to reconnect ${connectionKey}...`);
        this.connectToChannel(channel, storeId ? { storeId } : {});
      }, this.config.reconnectInterval);
      this.reconnectTimeouts.set(connectionKey, timeout);
    }
  }

  private clearReconnectTimeout(connectionKey: string): void {
    const timeout = this.reconnectTimeouts.get(connectionKey);
    if (timeout) {
      clearTimeout(timeout);
      this.reconnectTimeouts.delete(connectionKey);
    }
  }

  public disconnectChannel(channel: WebSocketChannel, storeId?: string): void {
    const connectionKey = this.getConnectionKey(channel, storeId);
    const connection = this.connections.get(connectionKey);

    if (connection) {
      this.clearReconnectTimeout(connectionKey);
      connection.close();
      this.connections.delete(connectionKey);
      this.connectionAttempts.delete(connectionKey); // Clear any connection attempt flags
      this.store.setConnectionStatus(channel, { isConnected: false }, storeId);
    }
  }

  public disconnectAll(): void {
    for (const connectionKey of this.connections.keys()) {
      const [channel, storeId] = connectionKey.split("-");
      this.disconnectChannel(channel as WebSocketChannel, storeId);
    }
  }

  public isConnected(channel: WebSocketChannel, storeId?: string): boolean {
    const connection = this.connections.get(
      this.getConnectionKey(channel, storeId)
    );
    return connection?.readyState === WebSocket.OPEN;
  }

  public sendToChannel(
    channel: WebSocketChannel,
    type: string,
    data: unknown,
    storeId?: string
  ): void {
    const connection = this.connections.get(
      this.getConnectionKey(channel, storeId)
    );
    if (connection?.readyState === WebSocket.OPEN) {
      connection.send(
        JSON.stringify({ type, data, channel, timestamp: Date.now() })
      );
    } else {
      console.error(
        `Cannot send message: WebSocket is not connected for ${channel}`
      );
    }
  }
}
