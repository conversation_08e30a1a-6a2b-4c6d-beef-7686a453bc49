import type { WorkSetup, SalaryPerioud } from "@/stores/types/auth.types";

export type ProviderLoginTypes = "linkedin" | "google";
export type Role = "JOBSEEKER" | "RECRUITER" | "ADMIN";
export type RoleShortened = "js" | "hr" | "admin";


export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}


export type SearchType = "dreamRoles" | "places";

// ONBOARDING FIRST STEP
interface Location {
  lat: number;
  lng: number;
}

interface State {
  code: string;
  name: string;
}

interface CountryInfo {
  code: string;
  name: string;
}

interface Country {
  placeId: string;
  name: string;
  country: CountryInfo;
  city: string;
  district: string;
  zipCode: string;
  formattedAddress: string;
  location: Location;
  state: State;
}

export enum LocationPurpose {
  ORIGIN = 'ORIGIN',
  PREFERRED = 'PREFERRED',
  RELOCATION = 'RELOCATION',
  OTHER = 'OTHER'
}

export interface OnboardingFirstStepData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  placeId: string;
  name: string;
  countryCode: string;
  countryName: string;
  city: string;
  district: string;
  zipCode: string;
  formattedAddress: string;
  latitude: number;
  longitude: number;
  purpose: LocationPurpose;
  stateCode: string;
  stateName: string;
}


export interface OnboardingSecondStepData {
  dreamRoleId: string;
  currency: string;
  fromRangeSalary: number;
  toRangeSalary: number;
  yearsOfExperience: number;
  workSetup: WorkSetup;
  desiredSalaryFrequency: SalaryPerioud;
  placeId: string;
  name: string;
  countryCode: string;
  countryName: string;
  city: string;
  district: string;
  zipCode: string;
  formattedAddress: string;
  latitude: number;
  longitude: number;
  purpose: "HOME" | "PREFERRED";
  stateCode: string;
  stateName: string;
}
