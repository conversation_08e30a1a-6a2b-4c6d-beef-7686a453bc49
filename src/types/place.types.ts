

import type { SearchType } from "@/types/general.types"
interface Location {
    lat: number;
    lng: number;
}

interface Country {
    code: string;
    name: string;
}

interface State {
    code: string;
    name: string;
}

export interface Place {
    placeId: string;
    name: string;
    country: Country;
    city: string;
    district: string;
    zipCode: string;
    formattedAddress: string;
    location: Location;
    state: State;
}

export interface PlaceResponse {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  results: Place[];
}


export interface PlaceSearchQuery {
  q: string;
  type: SearchType;
}
