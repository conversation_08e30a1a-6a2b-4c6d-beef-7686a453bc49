import type { SearchType } from "@/types/general.types"
export interface Job {
    id: string;
    industries: string[];
    name: string;
    seniority: string;
    skills: string[];
    snowID: string;
}

export interface JobResponse {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    results: Job[];
}



export interface JobSearchQuery {
  type: SearchType;
  q: string;
}
