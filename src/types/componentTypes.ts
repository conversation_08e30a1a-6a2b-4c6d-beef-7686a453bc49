import type { IconName } from "../components/ff/iconMap";
import type { RouteLocationRaw } from "vue-router";

export type ButtonType =
  | "none"
  | "primary"
  | "secondary"
  | "white"
  | "flat"
  | "dark"
  | "error"
  | "bordered";
export type ButtonSize = "lg" | "md" | "sm";
export type ButtonVariant = "white" | undefined;

export interface ButtonProps {
  /** Button text content */
  text?: string;
  /** Button type/style */
  type: ButtonType;
  /** Button size */
  size?: ButtonSize;
  /** Invert the button colors */
  colorInverted?: boolean;
  /** Show loading state */
  isLoading?: boolean;
  /** Route to navigate to (for router-link) */
  to?: RouteLocationRaw | string;
  /** URL for anchor tag */
  href?: string;
  /** Disable the button */
  disabled?: boolean;
  /** Force button element */
  isButton?: boolean;
  /** Use router-link */
  isRoute?: boolean;
  /** Use anchor tag */
  isLink?: boolean;
  /** Box style button */
  isBox?: boolean;
  /** Active state */
  active?: boolean;
  /** Button variant */
  variant?: ButtonVariant;
  /** Submit button type */
  isSubmit?: boolean;
  /** Left icon name */
  leftIcon?: IconName;
  /** Right icon name */
  rightIcon?: IconName;
  /** Full width button */
  fullwidth?: boolean;
}
