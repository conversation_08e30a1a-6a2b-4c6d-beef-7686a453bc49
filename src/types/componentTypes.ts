import type { IconName } from "../components/ff/iconMap";
import type { RouteLocationRaw } from "vue-router";

export type ButtonType =
  | "none"
  | "primary"
  | "secondary"
  | "white"
  | "flat"
  | "dark"
  | "error"
  | "bordered";
export type ButtonSize = "lg" | "md" | "sm";
export type ButtonVariant = "white" | undefined;

// Checkbox component types
export type CheckboxSize = "sm" | "md" | "lg";
export type CheckboxBackground = "white" | "light" | "dark";
export type CheckboxBorderRadius = "sm" | "md" | "lg";
export type CheckboxShadow = "none" | "subtle" | "prominent";

export interface ButtonProps {
  /** Button text content */
  text?: string;
  /** Button type/style */
  type: ButtonType;
  /** Button size */
  size?: ButtonSize;
  /** Invert the button colors */
  colorInverted?: boolean;
  /** Show loading state */
  isLoading?: boolean;
  /** Route to navigate to (for router-link) */
  to?: RouteLocationRaw | string;
  /** URL for anchor tag */
  href?: string;
  /** Disable the button */
  disabled?: boolean;
  /** Force button element */
  isButton?: boolean;
  /** Use router-link */
  isRoute?: boolean;
  /** Use anchor tag */
  isLink?: boolean;
  /** Box style button */
  isBox?: boolean;
  /** Active state */
  active?: boolean;
  /** Button variant */
  variant?: ButtonVariant;
  /** Submit button type */
  isSubmit?: boolean;
  /** Left icon name */
  leftIcon?: IconName;
  /** Right icon name */
  rightIcon?: IconName;
  /** Full width button */
  fullwidth?: boolean;
}

export interface CheckboxProps {
  /** Checkbox model value */
  modelValue?: boolean;
  /** Checkbox title */
  title: string;
  /** Checkbox description */
  description?: string;
  /** Input name attribute */
  name?: string;
  /** Disable the checkbox */
  disabled?: boolean;
  /** Required field */
  required?: boolean;
  /** Input id attribute */
  id?: string;
  /** Left icon name */
  leftIcon?: IconName;
  /** Right icon name */
  rightIcon?: IconName;
  /** Route to navigate to (for router-link) */
  to?: RouteLocationRaw | string;
  /** Use router-link */
  isRoute?: boolean;
  /** Checkbox size */
  size?: CheckboxSize;
  /** Background color variant */
  background?: CheckboxBackground;
  /** Border radius variant */
  borderRadius?: CheckboxBorderRadius;
  /** Shadow variant */
  shadow?: CheckboxShadow;
}
