{"name": "talentflow-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@coders-tm/vue-number-format": "^3.35.4", "@internationalized/date": "^3.7.0", "@lbgm/phone-number-input": "^1.1.6-alpha.2", "@types/date-fns": "^2.6.3", "@types/vue-i18n": "^7.0.0", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^12.5.0", "axios": "^1.7.9", "country-codes-flags-phone-codes": "^1.1.1", "date-fns": "^4.1.0", "libphonenumber-js": "^1.12.8", "lucide-vue-next": "^0.474.0", "mitt": "^3.0.1", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.2.0", "radix-vue": "^1.9.13", "uuid": "^11.0.5", "v-money3": "^3.24.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-i18n": "^11.1.1", "vue-router": "^4.5.0", "vue3-lottie": "^3.3.1", "zod": "^3.25.49"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.2.0", "@types/node": "^22.13.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.28.0", "eslint-plugin-vue": "^10.2.0", "postcss": "^8.5.1", "sass-embedded": "^1.83.4", "typescript": "~5.6.2", "vite": "^6.0.5", "vite-plugin-eslint": "^1.8.1", "vue-tsc": "^2.2.0"}}