<svg viewBox="0 0 45.370599999999996 47.91" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" data-name="Layer 2" id="Layer_2" style="max-height: 500px" width="45.370599999999996" height="47.91">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: #2b3490;
      }

      .cls-6 {
        fill: url(#linear-gradient-8);
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: url(#linear-gradient-9);
      }

      .cls-9 {
        fill: url(#linear-gradient-6);
      }

      .cls-10 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient gradientUnits="userSpaceOnUse" gradientTransform="translate(25.26 -5.58) rotate(50.35)" y2="2.3" x2="20.18" y1="24.3" x1="6.29" id="linear-gradient">
      <stop stop-color="#2d3f99" offset="0"/>
      <stop stop-color="#9acdef" offset="1"/>
    </linearGradient>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(43.59 3.14) rotate(90.35)" y2="2.3" x2="20.18" id="linear-gradient-2"/>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(52.02 21.61) rotate(130.35)" y2="2.3" x2="20.18" y1="24.3" id="linear-gradient-3"/>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(46.61 41.17) rotate(170.35)" y2="2.3" x2="20.18" y1="24.3" x1="6.29" id="linear-gradient-4"/>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(29.88 52.68) rotate(-149.65)" y2="2.3" x2="20.18" y1="24.3" x1="6.29" id="linear-gradient-5"/>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(9.68 50.75) rotate(-109.65)" y2="2.3" x2="20.18" id="linear-gradient-6"/>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(-4.56 36.28) rotate(-69.65)" y2="2.3" x2="20.18" y1="24.3" x1="6.29" id="linear-gradient-7"/>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(-6.17 16.05) rotate(-29.65)" id="linear-gradient-8"/>
    <linearGradient xlink:href="#linear-gradient" gradientTransform="translate(5.61 -.49) rotate(10.35)" y2="2.3" x2="20.18" y1="24.3" x1="6.29" id="linear-gradient-9"/>
  </defs>
  <g data-name="Layer 1" id="Layer_1-2">
    <g>
      <g>
        <path d="M64.34,9.66l.49.26c5.93-3.45,13.81-3.23,19.18,1.18,14.92,12.23.42,35.63-17.12,27.9-14.18-6.24-8.67-23.4-9.75-35.57.32-2.98,3.55-4.46,5.98-2.62.32.24,1.21,1.39,1.21,1.66v7.19ZM82.8,23.96c0-5.07-4.11-9.18-9.18-9.18s-9.18,4.11-9.18,9.18,4.11,9.18,9.18,9.18,9.18-4.11,9.18-9.18Z" class="cls-5"/>
        <path d="M207.54,38.08v7.07c0,.35-.88,1.61-1.21,1.9-2.23,1.93-5.58.4-5.99-2.38.47-7.28-.62-15.32.04-22.51,1.3-14.14,19.68-19.79,28.88-9.03,10.76,12.59-1.38,31.35-17.69,26.55-1.46-.43-2.58-1.37-4.03-1.61ZM226.02,23.96c0-5.09-4.13-9.22-9.22-9.22s-9.22,4.13-9.22,9.22,4.13,9.22,9.22,9.22,9.22-4.13,9.22-9.22Z" class="cls-5"/>
        <path d="M197.48,23.96c0,9.1-7.38,16.47-16.48,16.47s-16.48-7.38-16.48-16.47,7.38-16.47,16.48-16.47,16.48,7.38,16.48,16.47ZM190.2,23.97c0-5.09-4.13-9.22-9.22-9.22s-9.22,4.13-9.22,9.22,4.13,9.22,9.22,9.22,9.22-4.13,9.22-9.22Z" class="cls-5"/>
        <path d="M161.68,23.96c0,9.1-7.38,16.47-16.48,16.47s-16.48-7.37-16.48-16.47,7.38-16.47,16.48-16.47,16.48,7.37,16.48,16.47ZM154.39,23.96c0-5.09-4.13-9.21-9.22-9.21s-9.22,4.13-9.22,9.21,4.13,9.21,9.22,9.21,9.22-4.13,9.22-9.21Z" class="cls-5"/>
        <path d="M125.93,23.97c0,9.1-7.38,16.47-16.48,16.47s-16.48-7.37-16.48-16.47,7.38-16.47,16.48-16.47,16.48,7.37,16.48,16.47ZM118.65,23.96c0-5.09-4.13-9.21-9.22-9.21s-9.22,4.13-9.22,9.21,4.13,9.21,9.22,9.21,9.22-4.13,9.22-9.21Z" class="cls-5"/>
        <path d="M261.03,40.48c-8.09-.35-14.33-6.42-14.88-14.5-.27-3.95-.35-10.76,0-14.65.46-5.14,6.91-4.95,7.45-.23s-.42,10.34,0,15.12c.43,4.99,5.51,7.91,10.19,6.57,8.75-2.51,4.16-16.16,5.47-22.59.72-3.55,6.68-3.64,7.14.66.41,3.8.3,11.46,0,15.36-.22,2.98-1.12,5-2.55,7.52-2.06,3.64-5.1,8.81-7.53,12.13-1.25,1.71-3.09,2.71-5.13,1.52-2.89-1.69-1.68-4.74-.16-6.92Z" class="cls-5"/>
        <path d="M242.24.99c.44.44.97,1.57,1.05,2.19v34.05c-.71,4.13-6.06,4.25-7.19.22l-.02-34.28c.41-2.86,4.08-4.26,6.16-2.19Z" class="cls-5"/>
      </g>
      <g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_">
          <path d="M10.52,24.03c-.93-.61-1.58-1.63-1.68-2.83-.58-7.05,3.16-13.61,9.52-16.72,6.17-3.02,13.51-2.05,18.7,2.46,1.56,1.36,1.72,3.72.37,5.28-1.36,1.56-3.73,1.72-5.28.37-2.91-2.53-7.03-3.07-10.49-1.38-3.57,1.74-5.66,5.43-5.34,9.38.17,2.06-1.37,3.87-3.43,4.04-.86.07-1.69-.16-2.36-.6Z" class="cls-10"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-2">
          <path d="M13.26,16.36c-.32-1.07-.16-2.27.53-3.24,4.09-5.77,11.17-8.4,18.04-6.69,6.67,1.66,11.67,7.12,12.74,13.9.32,2.04-1.07,3.96-3.12,4.28-2.04.32-3.97-1.08-4.28-3.12-.61-3.81-3.41-6.87-7.15-7.8-3.85-.96-7.82.52-10.12,3.75-1.2,1.69-3.53,2.09-5.22.89-.71-.5-1.19-1.21-1.42-1.98Z" class="cls-1"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-3">
          <path d="M20.29,12.24c.44-1.02,1.33-1.84,2.49-2.14,6.84-1.79,13.96.75,18.12,6.47,4.04,5.56,4.36,12.95.82,18.84-1.07,1.77-3.37,2.34-5.14,1.28-1.77-1.07-2.35-3.38-1.28-5.14,1.98-3.31,1.81-7.45-.46-10.57-2.34-3.21-6.33-4.63-10.17-3.63-2,.52-4.05-.67-4.57-2.68-.22-.84-.14-1.69.18-2.43Z" class="cls-3"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-4">
          <path d="M28.32,13.61c1-.5,2.2-.55,3.29-.04,6.39,3.03,10.21,9.54,9.72,16.6-.48,6.86-4.98,12.73-11.48,14.96-1.96.67-4.09-.37-4.76-2.32-.67-1.96.37-4.09,2.32-4.76,3.65-1.26,6.17-4.55,6.44-8.39.27-3.96-1.87-7.62-5.45-9.31-1.87-.88-2.67-3.12-1.78-4.99.37-.78.98-1.38,1.7-1.74Z" class="cls-2"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-5">
          <path d="M33.6,19.81c1.08.26,2.04.99,2.54,2.08,2.95,6.43,1.69,13.87-3.22,18.97-4.77,4.95-12,6.55-18.41,4.08-1.93-.74-2.89-2.91-2.15-4.84.74-1.93,2.92-2.9,4.84-2.15,3.6,1.38,7.65.48,10.33-2.29,2.76-2.86,3.47-7.03,1.81-10.64-.86-1.88-.04-4.1,1.84-4.97.79-.36,1.64-.43,2.42-.24Z" class="cls-4"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-6">
          <path d="M33.65,27.96c.67.89.93,2.07.61,3.23-1.87,6.82-7.63,11.71-14.66,12.46-6.83.72-13.4-2.7-16.73-8.71-1-1.81-.35-4.09,1.46-5.09,1.81-1,4.1-.34,5.09,1.46,1.87,3.37,5.55,5.29,9.38,4.88,3.95-.42,7.18-3.16,8.23-6.99.55-1.99,2.61-3.17,4.6-2.62.84.23,1.53.73,2.01,1.37Z" class="cls-9"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-7">
          <path d="M28.46,34.23c-.06,1.11-.62,2.18-1.61,2.87-5.82,4.02-13.37,4.07-19.24.12C1.91,33.38-.92,26.54.39,19.8c.4-2.03,2.36-3.36,4.39-2.96,2.03.4,3.36,2.37,2.96,4.39-.73,3.79.85,7.62,4.05,9.77,3.29,2.22,7.53,2.19,10.79-.07,1.7-1.18,4.03-.75,5.21.95.49.71.7,1.54.66,2.34Z" class="cls-7"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-8">
          <path d="M20.44,35.7c-.76.81-1.88,1.27-3.08,1.16-7.04-.66-12.86-5.48-14.81-12.28-1.9-6.6.33-13.66,5.67-17.98,1.61-1.3,3.97-1.05,5.27.56,1.3,1.61,1.05,3.97-.56,5.27-3,2.43-4.25,6.38-3.18,10.09,1.1,3.82,4.36,6.52,8.31,6.89,2.06.19,3.57,2.02,3.38,4.08-.08.86-.45,1.63-1,2.22Z" class="cls-6"/>
        </g>
        <g data-name="&amp;lt;Radial Repeat&amp;gt;" id="_Radial_Repeat_-9">
          <path d="M13.36,31.67c-1.11.13-2.26-.23-3.1-1.09-4.97-5.03-6.33-12.46-3.46-18.93C9.59,5.38,15.84,1.4,22.7,1.53c2.07.04,3.71,1.74,3.68,3.81-.04,2.07-1.75,3.72-3.81,3.68-3.86-.06-7.36,2.16-8.92,5.69-1.61,3.63-.85,7.8,1.94,10.62,1.45,1.47,1.44,3.84-.03,5.3-.62.61-1.39.96-2.19,1.06Z" class="cls-8"/>
        </g>
      </g>
    </g>
  </g>
</svg>