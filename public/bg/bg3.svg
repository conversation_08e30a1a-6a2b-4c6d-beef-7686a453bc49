<svg width="650" height="390" viewBox="0 0 650 390" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_31_29121)">
<ellipse cx="325" cy="66" rx="241" ry="240" fill="url(#paint0_radial_31_29121)"/>
</g>
<defs>
<filter id="filter0_f_31_29121" x="0" y="-258" width="650" height="648" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="42" result="effect1_foregroundBlur_31_29121"/>
</filter>
<radialGradient id="paint0_radial_31_29121" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(325 66) rotate(90) scale(240 567.756)">
<stop stop-color="#DDFEEF"/>
<stop offset="1" stop-color="#F1F4FF"/>
</radialGradient>
</defs>
</svg>
