<svg width="160" height="120" viewBox="0 0 160 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="81.5" cy="52" r="52" fill="#EAECF0"/>
<g filter="url(#filter0_dd_610_8914)">
<path d="M47.2266 79.1098L80.4651 63.6104C82.1339 62.8322 82.8559 60.8485 82.0777 59.1797L62.4484 17.0845L49.1563 12.2466L21.9611 24.9279C20.2923 25.7061 19.5703 27.6898 20.3485 29.3586L42.7959 77.4971C43.574 79.166 45.5577 79.888 47.2266 79.1098Z" fill="url(#paint0_linear_610_8914)"/>
<path d="M49.1572 12.2471L62.4494 17.085L53.3843 21.3121L49.1572 12.2471Z" fill="#D0D5DD"/>
</g>
<g filter="url(#filter1_dd_610_8914)">
<path d="M63.1163 67.7831H99.7909C101.632 67.7831 103.125 66.2904 103.125 64.4491V18.0022L93.1228 8H63.1163C61.2749 8 59.7822 9.49271 59.7822 11.3341V64.4491C59.7822 66.2904 61.2749 67.7831 63.1163 67.7831Z" fill="url(#paint1_linear_610_8914)"/>
<path d="M93.123 8L103.125 18.0022H93.123V8Z" fill="#D0D5DD"/>
</g>
<g filter="url(#filter2_dd_610_8914)">
<path d="M81.975 63.5909L115.214 79.0903C116.882 79.8685 118.866 79.1465 119.644 77.4777L139.274 35.3825L134.436 22.0903L107.24 9.40903C105.572 8.63085 103.588 9.35286 102.81 11.0217L80.3623 59.1602C79.5842 60.8291 80.3062 62.8128 81.975 63.5909Z" fill="url(#paint2_linear_610_8914)"/>
<path d="M134.436 22.0898L139.273 35.382L130.208 31.1549L134.436 22.0898Z" fill="#D0D5DD"/>
</g>
<circle cx="26.5" cy="11" r="5" fill="#F2F4F7"/>
<circle cx="23.5" cy="109" r="7" fill="#F2F4F7"/>
<circle cx="150.5" cy="35" r="7" fill="#F2F4F7"/>
<circle cx="139.5" cy="8" r="4" fill="#F2F4F7"/>
<defs>
<filter id="filter0_dd_610_8914" x="-1.06055" y="8.01953" width="104.547" height="112.499" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_610_8914"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_610_8914"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow_610_8914"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_610_8914" result="effect2_dropShadow_610_8914"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_610_8914" result="shape"/>
</filter>
<filter id="filter1_dd_610_8914" x="39.7822" y="8" width="83.3428" height="99.7832" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_610_8914"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_610_8914"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow_610_8914"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_610_8914" result="effect2_dropShadow_610_8914"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_610_8914" result="shape"/>
</filter>
<filter id="filter2_dd_610_8914" x="58.9531" y="8" width="104.548" height="112.499" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_610_8914"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_610_8914"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow_610_8914"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_610_8914" result="effect2_dropShadow_610_8914"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_610_8914" result="shape"/>
</filter>
<linearGradient id="paint0_linear_610_8914" x1="45.2738" y1="79.2396" x2="18.7853" y2="31.4523" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4E7EC"/>
<stop offset="1" stop-color="#F9FAFB"/>
</linearGradient>
<linearGradient id="paint1_linear_610_8914" x1="61.2916" y1="67.0755" x2="57.4807" y2="12.571" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4E7EC"/>
<stop offset="1" stop-color="#F9FAFB"/>
</linearGradient>
<linearGradient id="paint2_linear_610_8914" x1="80.6204" y1="62.1785" x2="100.201" y2="11.17" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4E7EC"/>
<stop offset="1" stop-color="#F9FAFB"/>
</linearGradient>
</defs>
</svg>
