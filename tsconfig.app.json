{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "typeRoots": ["./node_modules/@types", "./src/types", "./node_modules/zod"],
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@components/*": ["./src/components/*"],
      "@55/*": ["./src/components/ff/*"],
      "@views/*": ["./src/views/*"],
      "@pages/*": ["./src/pages/*"],
      "@layouts/*": ["./src/layouts/*"],
      "@events": ["./src/utils/eventBus"],
      "@lib/*": ["./src/lib/*"],
      "@types/*": ["./src/types/*"],
      "@store-types/*": ["./src/store/types/*"],
      "@validation-schemas/*": ["./src/utils/validation-schemas/*"],
      "@utils/*": ["./src/utils/*"]
    },
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "public/animations/spinner.vue"
  ]
}
