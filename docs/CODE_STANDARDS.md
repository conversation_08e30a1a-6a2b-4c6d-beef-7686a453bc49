# Code Standards

## Store Guidelines

### 1. No Direct API Calls in Stores

Stores should NEVER make direct API calls. All API calls must be made through service classes.

❌ **Incorrect**:
```typescript
// auth.store.ts
export const useAuthStore = defineStore("auth", {
  actions: {
    async login(credentials) {
      // ❌ WRONG: Direct API call in store
      const response = await api.post('/auth/login', credentials);
      this.user = response.data;
    }
  }
});
```

✅ **Correct**:
```typescript
// auth.service.ts
export class AuthService {
  static async login(credentials) {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  }
}

// auth.store.ts
export const useAuthStore = defineStore("auth", {
  actions: {
    async login(credentials) {
      // ✅ CORRECT: Using service for API calls
      const userData = await AuthService.login(credentials);
      this.user = userData;
    }
  }
});
```

### 2. Store Responsibilities

Stores should only be responsible for:
- Managing state
- Providing getters for computed values
- Updating state through actions
- Persisting state when needed

## Service Guidelines

### 1. Service Class Structure

All services must follow this exact pattern:

```typescript
export class ServiceName {
  // 1. Private static readonly BASE_PATH
  private static readonly BASE_PATH = '/api/endpoint';

  // 2. Static async methods with proper typing
  static async methodName(
    params: ParamType
  ): Promise<ApiResponse<ReturnType>> {
    try {
      // 3. Destructure data from api call
      const { data } = await api.method<ApiResponse<ReturnType>>(
        `${this.BASE_PATH}/endpoint`,
        params
      );
      return data;
    } catch (error) {
      // 4. Use ErrorHandler with service name
      ErrorHandler.handle("ServiceName::methodName", error);
    }
  }
}
```

### 2. Required Elements

Every service method must include:
- Proper TypeScript types for parameters and return values
- Try-catch block with ErrorHandler
- Consistent error handling pattern
- Proper API response typing
- Service name in error handler for traceability

### 3. Naming Conventions

- Service class names must end with "Service"
- Method names should be descriptive and follow camelCase
- API endpoint paths should be in kebab-case
- Constants should be UPPER_SNAKE_CASE

### 4. Error Handling

- Always use the centralized ErrorHandler
- Include service name and method name in error handler
- Never throw raw errors
- Always handle API errors gracefully

### 5. Type Safety

- Define interfaces for all request and response types
- Use ApiResponse generic type for all API responses
- Avoid using any type
- Use proper TypeScript generics for API calls

### 6. Method Organization

Methods should be organized in this order:
1. GET methods
2. POST methods
3. PUT methods
4. DELETE methods
5. PATCH methods
6. Custom/utility methods

### 7. Documentation

Each service method should include:
- JSDoc comments describing the method's purpose
- Parameter descriptions
- Return type descriptions
- Example usage (if complex)

Example of a properly documented service method:
```typescript
/**
 * Searches for job positions based on the provided query
 * @param searchQuery - The search parameters including query string and type
 * @returns Promise containing the search results
 * @example
 * const results = await AppService.searchJobPosition({ q: "developer", type: "full-time" });
 */
static async searchJobPosition(
  searchQuery: JobSearchQuery
): Promise<ApiResponse<Job>> {
  try {
    const { data } = await api.get<ApiResponse<Job>>(
      `${this.BASE_PATH}/search?q=${searchQuery.q}&type=${searchQuery.type}`
    );
    return data;
  } catch (error) {
    ErrorHandler.handle("AppService::searchJobPosition", error);
  }
}
```

## Enforcement

To enforce these standards:

1. Use ESLint rules to prevent direct API calls in stores
2. Code reviews should check for proper separation of concerns
3. Automated tests should verify the correct pattern is followed
4. Use TypeScript strict mode
5. Regular code reviews focusing on service implementation

## Benefits

Following these standards provides:
- Better separation of concerns
- Easier testing
- More maintainable code
- Better error handling
- Type safety
- Reusable API logic
- Consistent codebase
- Better debugging capabilities
- Improved error tracking 