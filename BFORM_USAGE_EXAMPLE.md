# BForm Component Usage

The `BForm` component automatically handles validation for child `B_Input` components using vee-validate and Zod schemas.

## Benefits

- ✅ Removes need for `validation-type` and `validate-on-blur` props on individual inputs
- ✅ Automatic validation using Zod schemas
- ✅ Centralized form state management
- ✅ Built-in error handling and display

## Basic Usage

### 1. Create a validation schema

```typescript
// utils/validation-schemas/loginSchema.ts
import { z } from "zod";
import { toTypedSchema } from "@vee-validate/zod";

export const loginSchema = toTypedSchema(
  z.object({
    email: z.string().email("Please enter a valid email"),
    password: z.string().min(8, "Password must be at least 8 characters"),
  })
);
```

### 2. Use BForm in your component

```vue
<template>
  <BForm
    :validation-schema="loginSchema"
    :initial-values="{ email: '', password: '' }"
    @submit="handleSubmit"
    @validation-change="onValidationChange"
  >
    <!-- Email input - automatically validates based on schema -->
    <B_Input
      label="Email"
      name="email"
      type="email"
      placeholder="Enter your email"
    />

    <!-- Password input - automatically validates based on schema -->
    <B_Input
      label="Password"
      name="password"
      type="password"
      placeholder="Enter your password"
    />

    <!-- Submit button -->
    <B_Button type="primary" :is-submit="true" :disabled="!isFormValid">
      Login
    </B_Button>
  </BForm>
</template>

<script setup lang="ts">
import { ref } from "vue";
import BForm from "@/components/ff/form.vue";
import B_Input from "@/components/ff/input.vue";
import B_Button from "@/components/ff/button.vue";
import { loginSchema } from "@/utils/validation-schemas/loginSchema";

const isFormValid = ref(false);

const handleSubmit = (values: any) => {
  console.log("Form submitted with values:", values);
  // Handle form submission
};

const onValidationChange = (isValid: boolean) => {
  isFormValid.value = isValid;
};
</script>
```

## Key Changes

### Before (with manual validation):

```vue
<form @submit.prevent="handleSubmit">
  <B_Input
    v-model="email"
    label="Email"
    validation-type="email"
    validate-on-blur
    :error-message="emailError"
  />
  
  <B_Input
    v-model="password"
    label="Password"
    validation-type="password"
    validate-on-blur
    :error-message="passwordError"
  />
</form>
```

### After (with BForm):

```vue
<BForm :validation-schema="loginSchema" @submit="handleSubmit">
  <B_Input
    label="Email"
    name="email"
  />
  
  <B_Input
    label="Password"
    name="password"
  />
</BForm>
```

## Important Props

### BForm Props:

- `validation-schema` (required): Zod schema wrapped with `toTypedSchema()`
- `initial-values` (optional): Initial form values
- `@submit`: Emitted when form is submitted with valid values
- `@validation-change`: Emitted when form validity changes

### B_Input Props:

- `name` (required when inside BForm): Field name matching schema
- No more `validation-type` or `validate-on-blur` needed!
- `error-message` can still be used for custom errors

## Notes

- The `B_Input` component automatically detects if it's inside a `BForm` and uses vee-validate
- If not inside a `BForm`, it falls back to the original validation behavior
- All existing `B_Input` props still work as before
- Form validation happens automatically on blur and input based on vee-validate defaults
